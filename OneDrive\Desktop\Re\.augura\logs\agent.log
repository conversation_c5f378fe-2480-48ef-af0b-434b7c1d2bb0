2025-08-04 11:13:18,142 - __main__ - INFO - Starting Augura Coder Agent
2025-08-04 11:13:18,142 - __main__ - INFO - Starting Augura Coder Agent
2025-08-04 11:13:18,291 - root - ERROR - Initialization failed: 'str' object has no attribute 'parent'
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\Re\agent_core\main.py", line 84, in initialize
    await self.ai_agent.initialize()
  File "C:\Users\<USER>\OneDrive\Desktop\Re\agent_core\ai_agent.py", line 112, in initialize
    self.indexer = ProjectIndexer(str(self.project_path), str(index_db_path))
                   ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Desktop\Re\agent_core\indexer.py", line 422, in __init__
    self.db_path.parent.mkdir(exist_ok=True)
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'str' object has no attribute 'parent'
2025-08-04 11:13:18,291 - root - ERROR - Initialization failed: 'str' object has no attribute 'parent'
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\Re\agent_core\main.py", line 84, in initialize
    await self.ai_agent.initialize()
  File "C:\Users\<USER>\OneDrive\Desktop\Re\agent_core\ai_agent.py", line 112, in initialize
    self.indexer = ProjectIndexer(str(self.project_path), str(index_db_path))
                   ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Desktop\Re\agent_core\indexer.py", line 422, in __init__
    self.db_path.parent.mkdir(exist_ok=True)
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'str' object has no attribute 'parent'
