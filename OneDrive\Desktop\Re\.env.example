# Augura Coder Agent Configuration
# Copy this file to .env and fill in your actual values

# ===== API KEYS =====
# Get your OpenRouter API key from: https://openrouter.ai/
OPENROUTER_API_KEY=your_openrouter_key_here

# Get your Groq API key from: https://console.groq.com/
GROQ_API_KEY=your_groq_key_here

# ===== MODEL CONFIGURATION =====
# Default model for complex tasks (OpenRouter)
DEFAULT_MODEL=openrouter/horizon-beta

# Fast model for simple tasks (Groq)
GROQ_MODEL=qwen/qwen3-32b

# Maximum tokens for AI responses
MAX_TOKENS=4000

# Temperature (0.0 = deterministic, 1.0 = creative)
TEMPERATURE=0.7

# ===== FEATURE TOGGLES =====
# Enable/disable agent memory and learning
MEMORY_ENABLED=true

# Enable/disable project indexing
INDEXING_ENABLED=true

# Enable/disable RAG (Retrieval-Augmented Generation)
RAG_ENABLED=true

# Enable/disable MCP (Model Context Protocol) server
MCP_ENABLED=true

# MCP server port
MCP_PORT=8765

# ===== DEBUGGING =====
# Enable debug mode for detailed logging
DEBUG_MODE=false

# Logging level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# ===== MEMORY SETTINGS =====
# Maximum number of conversations to remember
MAX_CONVERSATIONS=1000

# Maximum number of code interactions to remember
MAX_CODE_INTERACTIONS=500

# Memory retention period in days
MEMORY_RETENTION_DAYS=30

# ===== INDEXING SETTINGS =====
# Maximum file size to index (in bytes)
MAX_FILE_SIZE=1048576

# Number of worker threads for indexing
INDEXING_WORKERS=4

# Auto-reindex interval in hours
AUTO_REINDEX_HOURS=6

# ===== RAG SETTINGS =====
# Maximum context length for RAG
MAX_CONTEXT_LENGTH=8000

# Maximum number of retrieved chunks
MAX_RETRIEVED_CHUNKS=10

# Similarity threshold for retrieval
SIMILARITY_THRESHOLD=0.1

# Embedding model for semantic search
EMBEDDING_MODEL=all-MiniLM-L6-v2

# ===== PERFORMANCE =====
# Request timeout in seconds
REQUEST_TIMEOUT=30

# Enable telemetry (anonymous usage statistics)
ENABLE_TELEMETRY=false
