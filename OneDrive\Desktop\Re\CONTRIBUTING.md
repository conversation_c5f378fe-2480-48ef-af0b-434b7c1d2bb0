# Contributing to Augura Coder Agent

Thank you for your interest in contributing to Augura Coder Agent! We welcome contributions from the community.

## 🚀 Getting Started

### Prerequisites
- Node.js 16+
- Python 3.8+
- VS Code
- Git

### Development Setup

1. **Fork and clone the repository**
   ```bash
   git clone https://github.com/your-username/augura-coder-agent.git
   cd augura-coder-agent
   ```

2. **Install dependencies**
   ```bash
   # Node.js dependencies
   npm install
   
   # Python dependencies
   cd agent_core
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate.bat
   pip install -r requirements.txt
   cd ..
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env and add your API keys
   ```

4. **Run the extension**
   ```bash
   code .
   # Press F5 to launch Extension Development Host
   ```

## 🛠️ Development Guidelines

### Code Style

**Python**
- Follow PEP 8
- Use Black for formatting: `black agent_core/`
- Use type hints
- Add docstrings for all functions and classes

**JavaScript**
- Use ESLint configuration
- Use meaningful variable names
- Add JSDoc comments for functions

### Project Structure

```
augura-coder-agent/
├── agent_core/          # Python AI agent
│   ├── ai_agent.py     # Main agent logic
│   ├── indexer.py      # Project indexing
│   ├── rag_engine.py   # RAG implementation
│   ├── memory_manager.py # Memory system
│   └── config.py       # Configuration
├── src/                # VS Code extension
│   ├── extension.js    # Extension entry point
│   └── webview/        # Chat interface
├── template/           # UI templates
└── tests/              # Test files
```

## 🧪 Testing

### Running Tests

**Python tests**
```bash
cd agent_core
python -m pytest tests/
```

**Extension tests**
```bash
npm test
```

### Writing Tests

- Add unit tests for new Python functions
- Test VS Code commands and webview interactions
- Include integration tests for AI agent functionality

## 📝 Pull Request Process

1. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes**
   - Write clean, documented code
   - Add tests for new functionality
   - Update documentation as needed

3. **Test your changes**
   ```bash
   # Run all tests
   npm test
   cd agent_core && python -m pytest
   
   # Test the extension manually
   code . # Press F5 to test
   ```

4. **Commit your changes**
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   ```

5. **Push and create PR**
   ```bash
   git push origin feature/your-feature-name
   ```
   Then create a Pull Request on GitHub.

## 🐛 Bug Reports

When reporting bugs, please include:

- **Environment**: OS, VS Code version, Python version
- **Steps to reproduce**: Clear, step-by-step instructions
- **Expected behavior**: What should happen
- **Actual behavior**: What actually happens
- **Logs**: Any error messages or logs
- **Screenshots**: If applicable

## 💡 Feature Requests

For feature requests, please:

- Check existing issues first
- Describe the problem you're trying to solve
- Explain your proposed solution
- Consider the impact on existing functionality

## 🏷️ Commit Convention

We use conventional commits:

- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `style:` - Code style changes
- `refactor:` - Code refactoring
- `test:` - Adding or updating tests
- `chore:` - Maintenance tasks

## 📋 Areas for Contribution

### High Priority
- [ ] Improve error handling and user feedback
- [ ] Add more language support for code analysis
- [ ] Enhance memory and learning capabilities
- [ ] Optimize performance for large codebases

### Medium Priority
- [ ] Add more AI model providers
- [ ] Improve UI/UX of chat interface
- [ ] Add configuration validation
- [ ] Enhance testing coverage

### Low Priority
- [ ] Add themes and customization
- [ ] Create documentation website
- [ ] Add telemetry and analytics
- [ ] Internationalization support

## 🤝 Code of Conduct

- Be respectful and inclusive
- Focus on constructive feedback
- Help others learn and grow
- Maintain a positive environment

## 📞 Getting Help

- **Issues**: [GitHub Issues](https://github.com/augura/augura-coder-agent/issues)
- **Discussions**: [GitHub Discussions](https://github.com/augura/augura-coder-agent/discussions)
- **Discord**: [Join our Discord](https://discord.gg/augura-coder)

## 🎉 Recognition

Contributors will be:
- Listed in the README
- Mentioned in release notes
- Invited to the contributors team

Thank you for contributing to Augura Coder Agent! 🚀
