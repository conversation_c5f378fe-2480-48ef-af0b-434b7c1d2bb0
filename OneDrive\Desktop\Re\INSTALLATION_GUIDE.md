# 📦 Augura Coder Agent - Installation Guide

## ✅ Installation Complete!

Your Augura Coder Agent is now ready to use! Here's how to get started:

## 🚀 Quick Start

### Method 1: Run Extension in Development Mode (Recommended)

1. **Open VS Code in this directory:**
   ```bash
   code .
   ```

2. **Press F5** to launch the Extension Development Host
   - This will open a new VS Code window with the extension loaded
   - Look for the Augura icon in the activity bar

3. **In the new VS Code window:**
   - Open your project folder
   - Click the Augura icon or use Command Palette: "Augura: Open Chat"
   - Start chatting with your AI coding assistant!

### Method 2: Test Python Agent Standalone

1. **Run the test script:**
   ```bash
   python test_agent.py
   ```

2. **Or run interactively:**
   ```bash
   python agent_core\main.py . --interactive
   ```

## 🎯 First Steps

### 1. Open the Chat Interface
- Click the Augura icon in the activity bar
- Or use Command Palette (`Ctrl+Shift+P`): "Augura: Open Chat"

### 2. Try These Example Queries

**Get started:**
```
Hello! Can you help me understand this codebase?
```

**Analyze code:**
```
Analyze the main files in this project and give me an overview
```

**Get coding help:**
```
How can I improve the code quality in this project?
```

**Ask for specific help:**
```
Explain how the extension.js file works
```

## ⚙️ Configuration

### API Keys
Your API keys are already configured in `.env`:
- ✅ OpenRouter API Key: Configured
- ✅ Groq API Key: Configured

### Features Status
- 🤖 **AI Agent**: ✅ Working
- 💬 **Chat Interface**: ✅ Ready
- 🧠 **Memory System**: ⚠️ Disabled (for faster startup)
- 🔍 **Project Indexing**: ⚠️ Disabled (for faster startup)
- 🔄 **RAG Engine**: ⚠️ Disabled (for faster startup)

### Enable Advanced Features
To enable memory, indexing, and RAG, edit `.env`:
```env
MEMORY_ENABLED=true
INDEXING_ENABLED=true
RAG_ENABLED=true
```

## 🎨 Available Commands

### VS Code Commands (Command Palette)
- `Augura: Open Chat` - Open the chat interface
- `Augura: Start Agent` - Start the AI agent
- `Augura: Stop Agent` - Stop the AI agent
- `Augura: Analyze Current File` - Analyze the current file
- `Augura: Show Agent Statistics` - Show agent stats

### Context Menu (Right-click on code)
- **Analyze File** - Get file analysis
- **Explain Code** - Explain selected code
- **Refactor Code** - Get refactoring suggestions
- **Generate Tests** - Generate unit tests

## 🔧 Troubleshooting

### Common Issues

**"Extension not loading"**
- Make sure you pressed F5 in the main VS Code window
- Check that all dependencies are installed: `npm install`
- Try reloading the extension development window

**"Agent not responding"**
- Check that API keys are correctly set in `.env`
- Verify internet connection
- Check the VS Code Developer Console for errors

**"Python errors"**
- Make sure Python virtual environment is activated
- Install missing dependencies: `pip install -r agent_core/requirements.txt`

### Debug Mode
Enable debug mode for detailed logging:
```env
DEBUG_MODE=true
LOG_LEVEL=DEBUG
```

## 📁 Project Structure

```
augura-coder-agent/
├── 🐍 agent_core/          # Python AI agent
│   ├── ai_agent.py         # Main agent
│   ├── config.py           # Configuration
│   ├── main.py             # CLI entry point
│   └── requirements.txt    # Python deps
├── 📦 src/                 # VS Code extension
│   ├── extension.js        # Extension entry
│   └── webview/           # Chat interface
├── 🎨 template/            # UI templates
│   ├── chat.html          # Chat interface
│   └── scripts/           # JavaScript
├── ⚙️ .env                 # Configuration
├── 📋 package.json         # Extension manifest
└── 🚀 test_agent.py        # Test script
```

## 🎉 You're Ready!

Your Augura Coder Agent is now installed and ready to help you code smarter!

### Next Steps:
1. **Press F5** to launch the extension
2. **Open the chat** and start asking questions
3. **Explore the features** and commands
4. **Enable advanced features** when ready

### Need Help?
- Check the [README.md](README.md) for detailed documentation
- Run `python test_agent.py` to test the core agent
- Enable debug mode for troubleshooting

**Happy Coding! 🚀**
