# 🚀 Augura Coder Agent - Quick Start Guide

## ⚡ 5-Minute Setup

### 1. Prerequisites
- **Python 3.8+** ([Download](https://python.org/downloads/))
- **Node.js 16+** ([Download](https://nodejs.org/))
- **VS Code** ([Download](https://code.visualstudio.com/))
- **API Keys** (OpenRouter and/or Groq)

### 2. Get API Keys

#### OpenRouter (Recommended)
1. Go to [OpenRouter.ai](https://openrouter.ai/)
2. Sign up and get your API key
3. Provides access to GPT-4, Claude, and other models

#### Groq (Optional - for speed)
1. Go to [Groq Console](https://console.groq.com/)
2. Sign up and get your API key
3. Provides ultra-fast inference

### 3. Installation

#### Windows
```bash
# Run the setup script
start.bat
```

#### macOS/Linux
```bash
# Make script executable and run
chmod +x start.sh
./start.sh
```

#### Manual Installation
```bash
# Install Python dependencies
cd agent_core
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate.bat
pip install -r requirements.txt
cd ..

# Install Node.js dependencies
npm install
```

### 4. Configure API Keys

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` and add your API keys:
   ```env
   OPENROUTER_API_KEY=your_actual_key_here
   GROQ_API_KEY=your_actual_key_here
   ```

### 5. Run the Extension

#### In VS Code
1. Open this project in VS Code: `code .`
2. Press `F5` to launch the extension development host
3. In the new VS Code window, open your project
4. Open Command Palette (`Ctrl+Shift+P`)
5. Run "Augura: Start Agent"
6. Open the chat panel and start coding!

#### Standalone Mode
```bash
cd agent_core
source venv/bin/activate  # On Windows: venv\Scripts\activate.bat
python main.py /path/to/your/project --interactive
```

## 🎯 First Steps

### 1. Open Chat Interface
- Click the Augura icon in the activity bar
- Or use Command Palette: "Augura: Open Chat"

### 2. Try These Commands

**Analyze your project:**
```
Analyze this codebase and give me an overview
```

**Get help with specific code:**
```
Explain this function and suggest improvements
```

**Refactor code:**
```
Refactor this code to be more readable and efficient
```

**Generate tests:**
```
Generate unit tests for the selected function
```

### 3. Use Context Menu
- Right-click on any code file
- Select Augura options:
  - Analyze File
  - Explain Code
  - Refactor Code
  - Generate Tests

## 🔧 Configuration

### Basic Settings
Access via VS Code Settings (`Ctrl+,`) and search for "Augura":

- **API Keys**: Set your OpenRouter/Groq keys
- **Models**: Choose which AI models to use
- **Memory**: Enable/disable learning and memory
- **Indexing**: Configure project indexing
- **Debug**: Enable detailed logging

### Advanced Configuration
Edit `.augura/config.json` in your project:

```json
{
  "api": {
    "default_model": "openrouter/horizon-beta",
    "max_tokens": 4000,
    "temperature": 0.7
  },
  "memory": {
    "enabled": true,
    "retention_days": 30
  },
  "rag": {
    "enabled": true,
    "max_context_length": 8000
  }
}
```

## 🎨 Features Overview

### 🧠 **Smart Code Understanding**
- Analyzes your entire codebase
- Understands relationships between files
- Provides context-aware suggestions

### 💬 **Natural Language Interface**
- Chat with your code in plain English
- Ask questions, request changes, get explanations
- Streaming responses for real-time interaction

### 🗄️ **Persistent Memory**
- Remembers your coding patterns
- Learns your preferences over time
- Maintains conversation history

### 🔍 **Intelligent Search**
- Semantic code search
- Find code by meaning, not just keywords
- Cross-reference dependencies

### ⚡ **Real-time Actions**
- Apply code changes directly
- Create new files from suggestions
- Refactor with confidence

## 🐛 Troubleshooting

### Common Issues

**"Agent not responding"**
- Check API keys in `.env` file
- Verify internet connection
- Check VS Code Developer Console for errors

**"Project not indexed"**
- Run "Augura: Index Project" command
- Check file permissions
- Ensure project path is correct

**"Memory not working"**
- Check if memory is enabled in settings
- Verify `.augura` directory permissions
- Clear memory and restart: "Augura: Clear Memory"

**"Extension not loading"**
- Reload VS Code window (`Ctrl+Shift+P` → "Reload Window")
- Check extension is enabled
- Review VS Code logs

### Debug Mode
Enable debug mode for detailed logging:

1. Set `DEBUG_MODE=true` in `.env`
2. Or use VS Code setting: `augura.debugMode`
3. Check logs in `.augura/logs/agent.log`

### Getting Help
- **Issues**: [GitHub Issues](https://github.com/your-username/augura-coder-agent/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/augura-coder-agent/discussions)
- **Documentation**: [Full README](README.md)

## 🎉 You're Ready!

Your Augura Coder Agent is now set up and ready to help you code smarter, faster, and better!

**Pro Tips:**
- Start with simple questions to get familiar
- Use the context menu for quick actions
- Let the agent learn your coding style
- Explore the memory and learning features
- Check out the advanced configuration options

Happy coding! 🚀
