# 🤖 Augura Coder Agent

An advanced AI programming assistant built with <PERSON><PERSON><PERSON><PERSON>, RAG (Retrieval-Augmented Generation), and MCP (Model Context Protocol) technologies. Augura Coder provides intelligent code assistance with deep codebase understanding, local memory storage, and seamless VS Code integration.

## ✨ Features

### 🧠 **Advanced AI Capabilities**
- **Deep Codebase Understanding** - Analyzes and indexes your entire project
- **Intelligent Code Modifications** - Makes precise changes without breaking functionality
- **Smart Refactoring** - Improves code structure while maintaining quality
- **Bug Detection & Fixing** - Identifies and resolves issues automatically
- **Context-Aware Responses** - Uses RAG to provide relevant, accurate assistance

### 🗄️ **Memory & Learning System**
- **Persistent Memory** - Remembers conversations and code interactions
- **Pattern Learning** - Adapts to your coding style and preferences
- **Project Context** - Maintains understanding of project structure and dependencies
- **User Preferences** - Learns and applies your preferred coding patterns

### 🔍 **Project Indexing & Search**
- **Multi-Language Support** - Python, JavaScript, TypeScript, Java, C++, and more
- **Semantic Search** - Find code by meaning, not just keywords
- **Dependency Tracking** - Understands relationships between files and functions
- **Real-time Updates** - Keeps index synchronized with code changes

### 🌐 **Integration & Protocols**
- **VS Code Extension** - Seamless integration with your development environment
- **MCP Support** - Model Context Protocol for standardized AI communication
- **RESTful API** - Programmatic access to agent capabilities
- **WebSocket Streaming** - Real-time response streaming

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    VS Code Extension                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   Chat UI       │  │  Code Actions   │  │  Commands   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Agent Bridge (JS)                        │
│              Synchronous ↔ Asynchronous Bridge              │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   AI Agent Core (Python)                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   Query Router  │  │  Context Builder│  │  Response   │ │
│  │                 │  │                 │  │  Generator  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        ▼                     ▼                     ▼
┌─────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Memory    │    │   RAG Engine    │    │   Project       │
│   Manager   │    │                 │    │   Indexer       │
│             │    │ ┌─────────────┐ │    │                 │
│ ┌─────────┐ │    │ │  Retriever  │ │    │ ┌─────────────┐ │
│ │Patterns │ │    │ └─────────────┘ │    │ │  Code       │ │
│ └─────────┘ │    │ ┌─────────────┐ │    │ │  Analyzer   │ │
│ ┌─────────┐ │    │ │  Embeddings │ │    │ └─────────────┘ │
│ │History  │ │    │ └─────────────┘ │    │ ┌─────────────┐ │
│ └─────────┘ │    │ ┌─────────────┐ │    │ │  Semantic   │ │
│ ┌─────────┐ │    │ │  Context    │ │    │ │  Chunker    │ │
│ │Learning │ │    │ │  Builder    │ │    │ └─────────────┘ │
│ └─────────┘ │    │ └─────────────┘ │    └─────────────────┘
└─────────────┘    └─────────────────┘
        │                     │                     │
        ▼                     ▼                     ▼
┌─────────────────────────────────────────────────────────────┐
│                    Storage Layer                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   SQLite DB     │  │   Vector Store  │  │   File      │ │
│  │   (Memories)    │  │   (Embeddings)  │  │   System    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    External APIs                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   OpenRouter    │  │      Groq       │  │     MCP     │ │
│  │   (GPT-4, etc.) │  │   (Fast LLMs)   │  │   Servers   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- **Node.js** 16+ (for VS Code extension)
- **Python** 3.8+ (for AI agent core)
- **VS Code** (for the extension)
- **API Keys** (OpenRouter and/or Groq)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/augura-coder-agent.git
   cd augura-coder-agent
   ```

2. **Install Python dependencies**
   ```bash
   cd agent_core
   pip install -r requirements.txt
   ```

3. **Install VS Code extension**
   ```bash
   cd ..
   npm install
   npm run compile
   ```

4. **Configure API keys**
   Create a `.env` file in the project root:
   ```env
   OPENROUTER_API_KEY=your_openrouter_key_here
   GROQ_API_KEY=your_groq_key_here
   ```

5. **Install the extension in VS Code**
   - Open VS Code
   - Press `F5` to run the extension in development mode
   - Or package it: `vsce package` and install the `.vsix` file

### First Run

1. **Open your project in VS Code**
2. **Open the Command Palette** (`Ctrl+Shift+P`)
3. **Run "Augura: Start Agent"**
4. **Open the chat panel** and start coding!

## 📖 Usage

### Chat Interface

The main interface is a chat panel where you can:

- **Ask questions** about your code
- **Request modifications** to files
- **Get explanations** of complex code
- **Receive suggestions** for improvements

Example conversations:
```
You: Analyze the main.py file and suggest improvements

Augura: I've analyzed your main.py file. Here are the key findings:

📊 **Code Analysis Results**
- Total Lines: 245
- Functions: 12
- Classes: 3
- Complexity Score: 6.2/10

💡 **Suggestions**
1. The `process_data` function is quite complex (complexity: 8). Consider breaking it into smaller functions.
2. Add type hints to improve code readability
3. The error handling in `load_config` could be more specific

Would you like me to refactor any of these areas?
```

### Commands

Available VS Code commands:

- `Augura: Start Agent` - Initialize the AI agent
- `Augura: Stop Agent` - Stop the AI agent
- `Augura: Analyze Current File` - Analyze the currently open file
- `Augura: Refactor Selection` - Refactor selected code
- `Augura: Explain Code` - Explain selected code
- `Augura: Generate Tests` - Generate tests for selected code
- `Augura: Show Agent Stats` - Display agent statistics

### Code Actions

Right-click on code to access:

- **Explain this code** - Get detailed explanations
- **Refactor this** - Intelligent refactoring suggestions
- **Generate tests** - Create unit tests
- **Find similar code** - Locate similar patterns
- **Optimize performance** - Performance improvement suggestions

## ⚙️ Configuration

### Agent Configuration

The agent can be configured via `.augura/config.json`:

```json
{
  "api": {
    "openrouter_api_key": "",
    "groq_api_key": "",
    "default_model": "openrouter/horizon-beta",
    "groq_model": "qwen/qwen3-32b",
    "max_tokens": 4000,
    "temperature": 0.7
  },
  "memory": {
    "enabled": true,
    "memory_dir": ".augura/memory",
    "max_conversations": 1000,
    "retention_days": 30
  },
  "indexing": {
    "enabled": true,
    "index_db_path": ".augura/index.db",
    "supported_extensions": [".py", ".js", ".ts", ".java"],
    "max_workers": 4
  },
  "rag": {
    "enabled": true,
    "max_context_length": 8000,
    "similarity_threshold": 0.1,
    "embedding_model": "all-MiniLM-L6-v2"
  },
  "mcp": {
    "enabled": true,
    "port": 8765
  }
}
```

### Environment Variables

```env
# API Keys
OPENROUTER_API_KEY=your_key_here
GROQ_API_KEY=your_key_here

# Model Configuration
DEFAULT_MODEL=openrouter/horizon-beta
GROQ_MODEL=qwen/qwen3-32b

# Feature Toggles
MEMORY_ENABLED=true
INDEXING_ENABLED=true
RAG_ENABLED=true
MCP_ENABLED=true

# Debug
DEBUG_MODE=false
LOG_LEVEL=INFO
```

## 🔧 Development

### Project Structure

```
augura-coder-agent/
├── agent_core/                 # Python AI agent core
│   ├── ai_agent.py            # Main AI agent
│   ├── indexer.py             # Project indexing
│   ├── rag_engine.py          # RAG implementation
│   ├── memory_manager.py      # Memory system
│   ├── mcp_integration.py     # MCP protocol
│   ├── config.py              # Configuration
│   ├── main.py                # CLI entry point
│   └── requirements.txt       # Python dependencies
├── src/                       # VS Code extension
│   ├── extension.js           # Extension entry point
│   ├── agent/                 # Agent integration
│   ├── webview/               # Chat interface
│   └── commands/              # VS Code commands
├── template/                  # Chat UI template
│   ├── chat.html              # Main chat interface
│   ├── styles/                # CSS styles
│   └── scripts/               # JavaScript
├── package.json               # Extension manifest
└── README.md                  # This file
```

### Running in Development

1. **Start the Python agent**
   ```bash
   cd agent_core
   python main.py /path/to/your/project --interactive
   ```

2. **Run the VS Code extension**
   ```bash
   npm run compile
   # Press F5 in VS Code to launch extension development host
   ```

3. **Test the integration**
   - Open a project in the development VS Code window
   - Use the chat interface to interact with the agent

### Testing

```bash
# Python tests
cd agent_core
python -m pytest tests/

# Extension tests
npm test
```

### Building

```bash
# Build the extension
npm run compile

# Package the extension
vsce package
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

### Code Style

- **Python**: Follow PEP 8, use Black for formatting
- **JavaScript**: Use ESLint and Prettier
- **Documentation**: Update README and inline docs

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **LangChain** - For the AI framework
- **OpenRouter** - For AI model access
- **Groq** - For fast inference
- **VS Code** - For the excellent extension API
- **Tree-sitter** - For code parsing
- **scikit-learn** - For ML utilities

## 📞 Support

- **Issues**: [GitHub Issues](https://github.com/your-username/augura-coder-agent/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/augura-coder-agent/discussions)
- **Email**: <EMAIL>

## 🗺️ Roadmap

### Version 1.1
- [ ] Enhanced code generation
- [ ] Multi-file refactoring
- [ ] Git integration
- [ ] Plugin system

### Version 1.2
- [ ] Team collaboration features
- [ ] Cloud synchronization
- [ ] Advanced analytics
- [ ] Custom model support

### Version 2.0
- [ ] Multi-language support
- [ ] Advanced debugging assistance
- [ ] Code review automation
- [ ] Performance optimization

---

**Made with ❤️ by the Augura Team**
