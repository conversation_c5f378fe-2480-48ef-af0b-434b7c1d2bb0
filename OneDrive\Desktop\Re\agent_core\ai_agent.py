#!/usr/bin/env python3
"""
Advanced AI Agent for Augura Coder
Integrates all components for intelligent code assistance
"""

import os
import sys
import json
import asyncio
import aiohttp
import time
from typing import Dict, List, Any, Optional, Callable, AsyncGenerator
from dataclasses import dataclass
from pathlib import Path
import threading
import queue
import traceback

# Import our components
from indexer import ProjectIndexer
from rag_engine import RAGEngine
from memory_manager import MemoryManager

@dataclass
class AgentConfig:
    """Configuration for the AI Agent"""
    openrouter_api_key: str
    groq_api_key: str
    default_model: str = "openrouter/horizon-beta"
    groq_model: str = "qwen/qwen3-32b"
    max_tokens: int = 4000
    temperature: float = 0.7
    memory_dir: str = ".augura/memory"
    index_db: str = ".augura/index.db"
    enable_learning: bool = True
    enable_memory: bool = True
    enable_rag: bool = True

@dataclass
class AgentResponse:
    """Response from the AI Agent"""
    content: str
    actions: List[Dict[str, Any]]
    suggestions: List[str]
    confidence: float
    reasoning: str
    context_used: Dict[str, Any]
    execution_time: float

@dataclass
class CodeAction:
    """Represents a code action to be performed"""
    action_type: str  # 'create', 'modify', 'delete', 'move', 'rename'
    file_path: str
    content: Optional[str] = None
    line_start: Optional[int] = None
    line_end: Optional[int] = None
    description: str = ""
    dependencies: List[str] = None

class AIAgent:
    """Main AI Agent class"""

    def __init__(self, config: AgentConfig, project_path: str):
        self.config = config
        self.project_path = Path(project_path).resolve()
        self.session_id = f"session_{int(time.time())}"

        # Initialize components
        self.indexer = None
        self.rag_engine = None
        self.memory_manager = None

        # State management
        self.is_initialized = False
        self.current_context = {}
        self.conversation_history = []
        self.active_tasks = {}

        # Performance tracking
        self.stats = {
            'queries_processed': 0,
            'actions_executed': 0,
            'errors_encountered': 0,
            'average_response_time': 0.0
        }

        # Initialize async session
        self.session = None
        self.loop = None

    async def initialize(self):
        """Initialize the AI Agent"""
        try:
            print("Initializing Augura AI Agent...")

            # Create necessary directories
            memory_dir = self.project_path / self.config.memory_dir
            memory_dir.mkdir(parents=True, exist_ok=True)

            index_db_path = self.project_path / self.config.index_db
            index_db_path.parent.mkdir(parents=True, exist_ok=True)

            # Initialize components
            if self.config.enable_memory:
                self.memory_manager = MemoryManager(str(memory_dir))
                print("✓ Memory Manager initialized")

            if self.config.enable_rag:
                # Initialize indexer
                self.indexer = ProjectIndexer(str(self.project_path), str(index_db_path))

                # Check if we need to index the project
                if not index_db_path.exists():
                    print("Indexing project for the first time...")
                    await self._run_in_thread(self.indexer.index_project)
                    print("✓ Project indexed")

                # Initialize RAG engine
                self.rag_engine = RAGEngine(str(index_db_path))
                print("✓ RAG Engine initialized")

            # Initialize HTTP session
            self.session = aiohttp.ClientSession()

            # Load project context
            await self._load_project_context()

            self.is_initialized = True
            print("✓ AI Agent initialized successfully")

        except Exception as e:
            print(f"Error initializing AI Agent: {e}")
            traceback.print_exc()
            raise

    async def _run_in_thread(self, func, *args, **kwargs):
        """Run a blocking function in a thread"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, func, *args, **kwargs)

    async def _load_project_context(self):
        """Load project context"""
        try:
            # Get project info
            package_json = self.project_path / "package.json"
            if package_json.exists():
                with open(package_json) as f:
                    package_data = json.load(f)
                    self.current_context['project_info'] = package_data

            # Get project stats if indexer is available
            if self.indexer:
                stats = await self._run_in_thread(self.indexer.get_project_stats)
                self.current_context['project_stats'] = stats

            # Store project context in memory
            if self.memory_manager:
                await self._run_in_thread(
                    self.memory_manager.store_project_context,
                    str(self.project_path),
                    self.current_context.get('project_info', {}),
                    patterns=self.current_context.get('project_stats', {})
                )

        except Exception as e:
            print(f"Error loading project context: {e}")

    async def process_query(self, query: str, context: Dict[str, Any] = None) -> AgentResponse:
        """Process a user query and return a response"""
        start_time = time.time()

        try:
            if not self.is_initialized:
                await self.initialize()

            # Prepare context
            full_context = await self._prepare_context(query, context or {})

            # Determine query type and choose appropriate model
            query_type = self._analyze_query_type(query)
            use_groq = query_type in ['simple', 'quick_question']

            # Get RAG context if enabled
            rag_context = None
            if self.config.enable_rag and self.rag_engine:
                rag_context = await self._run_in_thread(
                    self.rag_engine.get_context_for_query, query, query_type
                )
                full_context['rag_context'] = rag_context

            # Generate response
            response_content = await self._generate_response(query, full_context, use_groq)

            # Process response and extract actions
            actions = self._extract_actions(response_content)
            suggestions = self._extract_suggestions(response_content)

            # Calculate confidence
            confidence = self._calculate_confidence(full_context, response_content)

            # Create response
            execution_time = time.time() - start_time
            response = AgentResponse(
                content=response_content,
                actions=actions,
                suggestions=suggestions,
                confidence=confidence,
                reasoning=self._generate_reasoning(full_context, query_type),
                context_used=full_context,
                execution_time=execution_time
            )

            # Store in memory
            if self.config.enable_memory and self.memory_manager:
                await self._run_in_thread(
                    self.memory_manager.store_conversation,
                    query, response_content, full_context
                )

            # Update stats
            self._update_stats(execution_time)

            return response

        except Exception as e:
            print(f"Error processing query: {e}")
            traceback.print_exc()

            # Return error response
            return AgentResponse(
                content=f"I encountered an error while processing your request: {str(e)}",
                actions=[],
                suggestions=["Please try rephrasing your question", "Check if all required files are accessible"],
                confidence=0.0,
                reasoning="Error occurred during processing",
                context_used={},
                execution_time=time.time() - start_time
            )

    async def process_query_stream(self, query: str, context: Dict[str, Any] = None) -> AsyncGenerator[str, None]:
        """Process a query with streaming response"""
        try:
            if not self.is_initialized:
                await self.initialize()

            # Prepare context
            full_context = await self._prepare_context(query, context or {})

            # Determine query type
            query_type = self._analyze_query_type(query)
            use_groq = query_type in ['simple', 'quick_question']

            # Get RAG context
            if self.config.enable_rag and self.rag_engine:
                rag_context = await self._run_in_thread(
                    self.rag_engine.get_context_for_query, query, query_type
                )
                full_context['rag_context'] = rag_context

            # Stream response
            async for chunk in self._generate_response_stream(query, full_context, use_groq):
                yield chunk

        except Exception as e:
            yield f"Error: {str(e)}"

    async def _prepare_context(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare comprehensive context for the query"""
        full_context = {
            'query': query,
            'session_id': self.session_id,
            'timestamp': time.time(),
            'project_path': str(self.project_path),
            'user_context': context
        }

        # Add current project context
        full_context.update(self.current_context)

        # Add recent conversation history
        if self.conversation_history:
            full_context['recent_conversations'] = self.conversation_history[-5:]

        # Get relevant memories
        if self.config.enable_memory and self.memory_manager:
            relevant_conversations = await self._run_in_thread(
                self.memory_manager.get_relevant_conversations, query, 3
            )
            if relevant_conversations:
                full_context['relevant_memories'] = [
                    {
                        'user_message': conv.user_message,
                        'assistant_response': conv.assistant_response,
                        'timestamp': conv.timestamp
                    }
                    for conv in relevant_conversations
                ]

            # Get learned patterns
            patterns = await self._run_in_thread(
                self.memory_manager.get_learned_patterns
            )
            if patterns:
                full_context['learned_patterns'] = [
                    {
                        'pattern_type': pattern.pattern_type,
                        'pattern_data': pattern.pattern_data,
                        'confidence': pattern.confidence
                    }
                    for pattern in patterns[:5]
                ]

        return full_context

    def _analyze_query_type(self, query: str) -> str:
        """Analyze the type of query"""
        query_lower = query.lower()

        # Code modification queries
        if any(word in query_lower for word in ['create', 'modify', 'change', 'update', 'fix', 'refactor']):
            return 'code_modification'

        # File operation queries
        if any(word in query_lower for word in ['file', 'directory', 'folder', 'move', 'delete', 'rename']):
            return 'file_operation'

        # Analysis queries
        if any(word in query_lower for word in ['analyze', 'explain', 'understand', 'review', 'check']):
            return 'analysis'

        # Simple questions
        if len(query) < 50 and ('?' in query or query_lower.startswith(('what', 'how', 'why', 'when', 'where'))):
            return 'simple'

        # Quick questions
        if any(word in query_lower for word in ['quick', 'briefly', 'short']):
            return 'quick_question'

        return 'complex'

    async def _generate_response(self, query: str, context: Dict[str, Any], use_groq: bool = False) -> str:
        """Generate AI response"""
        # Prepare messages
        messages = self._prepare_messages(query, context)

        # Choose API configuration
        if use_groq:
            api_config = {
                'url': 'https://api.groq.com/openai/v1/chat/completions',
                'headers': {
                    'Authorization': f'Bearer {self.config.groq_api_key}',
                    'Content-Type': 'application/json'
                },
                'model': self.config.groq_model
            }
        else:
            api_config = {
                'url': 'https://openrouter.ai/api/v1/chat/completions',
                'headers': {
                    'Authorization': f'Bearer {self.config.openrouter_api_key}',
                    'Content-Type': 'application/json'
                },
                'model': self.config.default_model
            }

        # Make API request
        payload = {
            'model': api_config['model'],
            'messages': messages,
            'temperature': self.config.temperature,
            'max_tokens': self.config.max_tokens
        }

        async with self.session.post(
            api_config['url'],
            headers=api_config['headers'],
            json=payload
        ) as response:
            if response.status == 200:
                data = await response.json()
                return data['choices'][0]['message']['content']
            else:
                error_text = await response.text()
                raise Exception(f"API request failed: {response.status} - {error_text}")

    async def _generate_response_stream(self, query: str, context: Dict[str, Any], use_groq: bool = False) -> AsyncGenerator[str, None]:
        """Generate streaming AI response"""
        # Prepare messages
        messages = self._prepare_messages(query, context)

        # Choose API configuration
        if use_groq:
            api_config = {
                'url': 'https://api.groq.com/openai/v1/chat/completions',
                'headers': {
                    'Authorization': f'Bearer {self.config.groq_api_key}',
                    'Content-Type': 'application/json'
                },
                'model': self.config.groq_model
            }
        else:
            api_config = {
                'url': 'https://openrouter.ai/api/v1/chat/completions',
                'headers': {
                    'Authorization': f'Bearer {self.config.openrouter_api_key}',
                    'Content-Type': 'application/json'
                },
                'model': self.config.default_model
            }

        # Make streaming API request
        payload = {
            'model': api_config['model'],
            'messages': messages,
            'temperature': self.config.temperature,
            'max_tokens': self.config.max_tokens,
            'stream': True
        }

        async with self.session.post(
            api_config['url'],
            headers=api_config['headers'],
            json=payload
        ) as response:
            if response.status == 200:
                async for line in response.content:
                    line_text = line.decode('utf-8').strip()

                    if line_text.startswith('data: '):
                        data_text = line_text[6:]

                        if data_text == '[DONE]':
                            break

                        try:
                            data = json.loads(data_text)
                            if 'choices' in data and len(data['choices']) > 0:
                                delta = data['choices'][0].get('delta', {})
                                content = delta.get('content', '')
                                if content:
                                    yield content
                        except json.JSONDecodeError:
                            continue
            else:
                error_text = await response.text()
                yield f"Error: API request failed: {response.status} - {error_text}"

    def _prepare_messages(self, query: str, context: Dict[str, Any]) -> List[Dict[str, str]]:
        """Prepare messages for AI API"""
        messages = []

        # System message
        system_prompt = self._generate_system_prompt(context)
        messages.append({
            'role': 'system',
            'content': system_prompt
        })

        # Add relevant conversation history
        if 'recent_conversations' in context:
            for conv in context['recent_conversations'][-3:]:  # Last 3 conversations
                messages.append({
                    'role': 'user',
                    'content': conv.get('user_message', '')
                })
                messages.append({
                    'role': 'assistant',
                    'content': conv.get('assistant_response', '')
                })

        # Current query
        enhanced_query = self._enhance_query_with_context(query, context)
        messages.append({
            'role': 'user',
            'content': enhanced_query
        })

        return messages

    def _generate_system_prompt(self, context: Dict[str, Any]) -> str:
        """Generate system prompt based on context"""
        prompt = """You are Augura Coder, an advanced AI programming assistant with deep understanding of codebases. You can:

1. **Analyze and understand code** with full context awareness
2. **Make precise code modifications** without breaking existing functionality
3. **Manage dependencies** and update related files automatically
4. **Perform intelligent refactoring** while maintaining code quality
5. **Create new files and features** that integrate seamlessly
6. **Detect and fix bugs** with comprehensive testing suggestions
7. **Optimize performance** and suggest best practices

**Current Project Context:**"""

        # Add project information
        if 'project_info' in context:
            project_info = context['project_info']
            prompt += f"\n- Project: {project_info.get('name', 'Unknown')}"
            prompt += f"\n- Description: {project_info.get('description', 'No description')}"
            if 'dependencies' in project_info:
                deps = list(project_info['dependencies'].keys())[:5]
                prompt += f"\n- Main Dependencies: {', '.join(deps)}"

        # Add project stats
        if 'project_stats' in context:
            stats = context['project_stats']
            prompt += f"\n- Total Files: {stats.get('total_files', 0)}"
            prompt += f"\n- Languages: {', '.join(stats.get('language_distribution', {}).keys())}"

        # Add RAG context
        if 'rag_context' in context:
            rag_context = context['rag_context']
            if rag_context.get('retrieved_context'):
                prompt += f"\n\n**Relevant Code Context:**"
                for i, result in enumerate(rag_context['retrieved_context'][:3]):
                    prompt += f"\n{i+1}. {result.source} (relevance: {result.relevance_score:.2f})"
                    prompt += f"\n   {result.content[:200]}..."

        # Add learned patterns
        if 'learned_patterns' in context:
            patterns = context['learned_patterns']
            if patterns:
                prompt += f"\n\n**Learned User Preferences:**"
                for pattern in patterns[:3]:
                    prompt += f"\n- {pattern['pattern_type']}: {pattern['pattern_data']}"

        prompt += """

**Guidelines:**
- Always consider the full codebase context when making changes
- Maintain existing code style and conventions
- Update all dependent files when making changes
- Provide clear explanations for your actions
- Suggest tests for new or modified code
- Be precise and avoid unnecessary changes
- Ask for clarification when requirements are ambiguous

**Response Format:**
Provide your response in a clear, structured format. If you're making code changes, explain:
1. What you're changing and why
2. How it affects other parts of the codebase
3. Any additional files that need updates
4. Testing recommendations

You can perform actual file operations and code modifications. Always explain your reasoning."""

        return prompt

    def _enhance_query_with_context(self, query: str, context: Dict[str, Any]) -> str:
        """Enhance query with additional context"""
        enhanced = query

        # Add current file context if available
        if 'current_file' in context.get('user_context', {}):
            current_file = context['user_context']['current_file']
            enhanced += f"\n\n**Current File:** {current_file.get('path', 'Unknown')}"
            if 'selected_code' in current_file:
                enhanced += f"\n**Selected Code:**\n```\n{current_file['selected_code']}\n```"

        # Add relevant memories
        if 'relevant_memories' in context:
            enhanced += f"\n\n**Related Previous Conversations:**"
            for i, memory in enumerate(context['relevant_memories'][:2]):
                enhanced += f"\n{i+1}. User: {memory['user_message'][:100]}..."
                enhanced += f"\n   Assistant: {memory['assistant_response'][:100]}..."

        return enhanced

    def _extract_actions(self, response: str) -> List[Dict[str, Any]]:
        """Extract actionable items from response"""
        actions = []

        # Look for file operation patterns
        import re

        # Create file patterns
        create_patterns = [
            r'create\s+(?:a\s+)?(?:new\s+)?file\s+[`"]?([^`"\s]+)[`"]?',
            r'new\s+file:\s*[`"]?([^`"\s]+)[`"]?',
            r'save\s+(?:this\s+)?(?:as\s+)?[`"]?([^`"\s]+)[`"]?'
        ]

        for pattern in create_patterns:
            matches = re.findall(pattern, response, re.IGNORECASE)
            for match in matches:
                actions.append({
                    'type': 'create_file',
                    'file_path': match,
                    'description': f'Create file {match}'
                })

        # Modify file patterns
        modify_patterns = [
            r'modify\s+(?:the\s+)?file\s+[`"]?([^`"\s]+)[`"]?',
            r'update\s+(?:the\s+)?file\s+[`"]?([^`"\s]+)[`"]?',
            r'change\s+(?:the\s+)?file\s+[`"]?([^`"\s]+)[`"]?'
        ]

        for pattern in modify_patterns:
            matches = re.findall(pattern, response, re.IGNORECASE)
            for match in matches:
                actions.append({
                    'type': 'modify_file',
                    'file_path': match,
                    'description': f'Modify file {match}'
                })

        # Code block extraction
        code_blocks = re.findall(r'```(\w+)?\n(.*?)```', response, re.DOTALL)
        for i, (language, code) in enumerate(code_blocks):
            actions.append({
                'type': 'code_suggestion',
                'language': language or 'text',
                'code': code.strip(),
                'description': f'Code suggestion {i+1}'
            })

        return actions

    def _extract_suggestions(self, response: str) -> List[str]:
        """Extract suggestions from response"""
        suggestions = []

        # Look for suggestion patterns
        import re

        suggestion_patterns = [
            r'(?:i\s+)?suggest(?:ion)?:?\s*(.+?)(?:\n|$)',
            r'(?:you\s+)?(?:should|could|might)\s+consider\s+(.+?)(?:\n|$)',
            r'recommendation:?\s*(.+?)(?:\n|$)',
            r'tip:?\s*(.+?)(?:\n|$)'
        ]

        for pattern in suggestion_patterns:
            matches = re.findall(pattern, response, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                suggestion = match.strip()
                if suggestion and len(suggestion) > 10:
                    suggestions.append(suggestion)

        return suggestions[:5]  # Limit to 5 suggestions

    def _calculate_confidence(self, context: Dict[str, Any], response: str) -> float:
        """Calculate confidence score for the response"""
        confidence = 0.5  # Base confidence

        # Higher confidence if we have good context
        if 'rag_context' in context:
            rag_context = context['rag_context']
            if rag_context.get('total_relevance', 0) > 2.0:
                confidence += 0.2

        # Higher confidence if we have relevant memories
        if 'relevant_memories' in context and context['relevant_memories']:
            confidence += 0.1

        # Higher confidence for shorter, more focused responses
        if len(response) < 1000:
            confidence += 0.1

        # Lower confidence if response contains uncertainty words
        uncertainty_words = ['might', 'maybe', 'possibly', 'not sure', 'unclear']
        if any(word in response.lower() for word in uncertainty_words):
            confidence -= 0.2

        # Higher confidence if response contains specific code or file references
        if '```' in response or any(ext in response for ext in ['.py', '.js', '.ts', '.java']):
            confidence += 0.1

        return max(0.0, min(1.0, confidence))

    def _generate_reasoning(self, context: Dict[str, Any], query_type: str) -> str:
        """Generate reasoning for the response"""
        reasoning_parts = []

        reasoning_parts.append(f"Query type identified as: {query_type}")

        if 'rag_context' in context:
            rag_context = context['rag_context']
            retrieved_count = len(rag_context.get('retrieved_context', []))
            if retrieved_count > 0:
                reasoning_parts.append(f"Retrieved {retrieved_count} relevant code contexts")

        if 'relevant_memories' in context:
            memory_count = len(context['relevant_memories'])
            if memory_count > 0:
                reasoning_parts.append(f"Found {memory_count} relevant previous conversations")

        if 'learned_patterns' in context:
            pattern_count = len(context['learned_patterns'])
            if pattern_count > 0:
                reasoning_parts.append(f"Applied {pattern_count} learned user preferences")

        return "; ".join(reasoning_parts)

    def _update_stats(self, execution_time: float):
        """Update performance statistics"""
        self.stats['queries_processed'] += 1

        # Update average response time
        current_avg = self.stats['average_response_time']
        count = self.stats['queries_processed']
        self.stats['average_response_time'] = (current_avg * (count - 1) + execution_time) / count

    async def execute_action(self, action: CodeAction) -> Dict[str, Any]:
        """Execute a code action"""
        try:
            result = {
                'success': False,
                'message': '',
                'changes': []
            }

            file_path = Path(self.project_path) / action.file_path

            if action.action_type == 'create':
                # Create new file
                file_path.parent.mkdir(parents=True, exist_ok=True)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(action.content or '')

                result['success'] = True
                result['message'] = f'Created file: {action.file_path}'
                result['changes'].append({
                    'type': 'create',
                    'file': action.file_path,
                    'content': action.content
                })

            elif action.action_type == 'modify':
                # Modify existing file
                if not file_path.exists():
                    result['message'] = f'File not found: {action.file_path}'
                    return result

                with open(file_path, 'r', encoding='utf-8') as f:
                    original_content = f.read()

                if action.line_start and action.line_end:
                    # Replace specific lines
                    lines = original_content.split('\n')
                    new_lines = lines[:action.line_start-1] + [action.content] + lines[action.line_end:]
                    new_content = '\n'.join(new_lines)
                else:
                    # Replace entire file
                    new_content = action.content

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)

                result['success'] = True
                result['message'] = f'Modified file: {action.file_path}'
                result['changes'].append({
                    'type': 'modify',
                    'file': action.file_path,
                    'before': original_content,
                    'after': new_content
                })

            elif action.action_type == 'delete':
                # Delete file
                if file_path.exists():
                    file_path.unlink()
                    result['success'] = True
                    result['message'] = f'Deleted file: {action.file_path}'
                    result['changes'].append({
                        'type': 'delete',
                        'file': action.file_path
                    })
                else:
                    result['message'] = f'File not found: {action.file_path}'

            # Store action in memory
            if self.config.enable_memory and self.memory_manager:
                await self._run_in_thread(
                    self.memory_manager.store_code_interaction,
                    action.file_path,
                    action.action_type,
                    code_after=action.content,
                    explanation=action.description,
                    success=result['success']
                )

            # Update stats
            if result['success']:
                self.stats['actions_executed'] += 1
            else:
                self.stats['errors_encountered'] += 1

            return result

        except Exception as e:
            self.stats['errors_encountered'] += 1
            return {
                'success': False,
                'message': f'Error executing action: {str(e)}',
                'changes': []
            }

    async def get_file_analysis(self, file_path: str) -> Dict[str, Any]:
        """Get comprehensive analysis of a file"""
        try:
            if not self.indexer:
                return {'error': 'Indexer not available'}

            file_info = await self._run_in_thread(
                self.indexer.get_file_info, file_path
            )

            if not file_info:
                return {'error': 'File not found in index'}

            # Get related code
            if self.rag_engine:
                related_code = await self._run_in_thread(
                    self.rag_engine.retriever.get_related_code,
                    file_path
                )
                file_info['related_code'] = related_code

            return file_info

        except Exception as e:
            return {'error': str(e)}

    async def search_codebase(self, query: str, search_type: str = 'all') -> List[Dict[str, Any]]:
        """Search the codebase"""
        try:
            if not self.indexer:
                return []

            results = await self._run_in_thread(
                self.indexer.search_code, query, search_type
            )

            return results

        except Exception as e:
            print(f"Error searching codebase: {e}")
            return []

    def get_stats(self) -> Dict[str, Any]:
        """Get agent statistics"""
        stats = dict(self.stats)

        if self.memory_manager:
            memory_stats = self.memory_manager.get_memory_stats()
            stats['memory'] = memory_stats

        if self.indexer:
            project_stats = self.indexer.get_project_stats()
            stats['project'] = project_stats

        return stats

    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.session:
                await self.session.close()

            if self.indexer:
                self.indexer.close()

            print("AI Agent cleanup completed")

        except Exception as e:
            print(f"Error during cleanup: {e}")

# Synchronous wrapper for use with VS Code extension
class AgentBridge:
    """Bridge between async AI Agent and synchronous VS Code extension"""

    def __init__(self, config: AgentConfig, project_path: str):
        self.agent = AIAgent(config, project_path)
        self.loop = None
        self.thread = None
        self.is_running = False

    def start(self):
        """Start the agent in a separate thread"""
        if self.is_running:
            return

        self.thread = threading.Thread(target=self._run_loop, daemon=True)
        self.thread.start()

        # Wait for initialization
        time.sleep(2)
        self.is_running = True

    def _run_loop(self):
        """Run the async event loop"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)

        try:
            self.loop.run_until_complete(self.agent.initialize())
            self.loop.run_forever()
        except Exception as e:
            print(f"Error in agent loop: {e}")
        finally:
            self.loop.close()

    def process_query(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Process query synchronously"""
        if not self.is_running:
            self.start()

        future = asyncio.run_coroutine_threadsafe(
            self.agent.process_query(query, context), self.loop
        )

        try:
            response = future.result(timeout=30)  # 30 second timeout
            return {
                'content': response.content,
                'actions': response.actions,
                'suggestions': response.suggestions,
                'confidence': response.confidence,
                'reasoning': response.reasoning,
                'execution_time': response.execution_time
            }
        except Exception as e:
            return {
                'content': f"Error processing query: {str(e)}",
                'actions': [],
                'suggestions': [],
                'confidence': 0.0,
                'reasoning': 'Error occurred',
                'execution_time': 0.0
            }

    def process_query_stream(self, query: str, context: Dict[str, Any] = None, callback=None):
        """Process query with streaming response"""
        if not self.is_running:
            self.start()

        async def stream_handler():
            try:
                async for chunk in self.agent.process_query_stream(query, context):
                    if callback:
                        callback(chunk)
            except Exception as e:
                if callback:
                    callback(f"Error: {str(e)}")

        asyncio.run_coroutine_threadsafe(stream_handler(), self.loop)

    def get_stats(self) -> Dict[str, Any]:
        """Get agent statistics"""
        if not self.is_running:
            return {}

        future = asyncio.run_coroutine_threadsafe(
            self._get_stats_async(), self.loop
        )

        try:
            return future.result(timeout=5)
        except Exception as e:
            return {'error': str(e)}

    async def _get_stats_async(self):
        """Get stats asynchronously"""
        return self.agent.get_stats()

    def stop(self):
        """Stop the agent"""
        if self.loop and self.is_running:
            asyncio.run_coroutine_threadsafe(self.agent.cleanup(), self.loop)
            self.loop.call_soon_threadsafe(self.loop.stop)
            self.is_running = False

if __name__ == '__main__':
    # Simple test
    import argparse

    parser = argparse.ArgumentParser(description='Augura AI Agent')
    parser.add_argument('project_path', help='Path to the project')
    parser.add_argument('--openrouter-key', required=True, help='OpenRouter API key')
    parser.add_argument('--groq-key', required=True, help='Groq API key')
    parser.add_argument('--query', help='Test query')

    args = parser.parse_args()

    # Create configuration
    config = AgentConfig(
        openrouter_api_key=args.openrouter_key,
        groq_api_key=args.groq_key
    )

    # Test with bridge
    bridge = AgentBridge(config, args.project_path)
    bridge.start()

    if args.query:
        print(f"Processing query: {args.query}")
        result = bridge.process_query(args.query)
        print(f"Response: {result['content']}")
        print(f"Confidence: {result['confidence']}")

    bridge.stop()
