#!/usr/bin/env python3
"""
Configuration management for Augura Coder Agent
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
import logging

@dataclass
class APIConfig:
    """API configuration"""
    openrouter_api_key: str = ""
    groq_api_key: str = ""
    openrouter_base_url: str = "https://openrouter.ai/api/v1/chat/completions"
    groq_base_url: str = "https://api.groq.com/openai/v1/chat/completions"
    default_model: str = "openrouter/horizon-beta"
    groq_model: str = "qwen/qwen3-32b"
    max_tokens: int = 4000
    temperature: float = 0.7
    timeout: int = 30

@dataclass
class MemoryConfig:
    """Memory system configuration"""
    enabled: bool = True
    memory_dir: str = ".augura/memory"
    max_conversations: int = 1000
    max_code_interactions: int = 500
    max_learning_patterns: int = 200
    cleanup_interval_hours: int = 24
    retention_days: int = 30

@dataclass
class IndexingConfig:
    """Indexing system configuration"""
    enabled: bool = True
    index_db_path: str = ".augura/index.db"
    max_file_size: int = 1048576  # 1MB
    supported_extensions: list = None
    ignore_patterns: list = None
    max_workers: int = 4
    auto_reindex_interval_hours: int = 6

@dataclass
class RAGConfig:
    """RAG system configuration"""
    enabled: bool = True
    max_context_length: int = 8000
    max_retrieved_chunks: int = 10
    similarity_threshold: float = 0.1
    use_semantic_search: bool = True
    embedding_model: str = "all-MiniLM-L6-v2"

@dataclass
class MCPConfig:
    """MCP server configuration"""
    enabled: bool = True
    port: int = 8765
    host: str = "localhost"
    max_connections: int = 10

@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = "INFO"
    log_file: str = ".augura/logs/agent.log"
    max_file_size: int = 10485760  # 10MB
    backup_count: int = 5
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

@dataclass
class AgentConfig:
    """Main agent configuration"""
    api: APIConfig
    memory: MemoryConfig
    indexing: IndexingConfig
    rag: RAGConfig
    mcp: MCPConfig
    logging: LoggingConfig
    
    # General settings
    project_path: str = ""
    debug_mode: bool = False
    enable_telemetry: bool = False
    
    def __post_init__(self):
        # Set default values for lists
        if self.indexing.supported_extensions is None:
            self.indexing.supported_extensions = [
                '.py', '.js', '.jsx', '.ts', '.tsx', '.java', '.cpp', '.c', '.h',
                '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala',
                '.html', '.css', '.scss', '.sass', '.json', '.xml', '.yaml', '.yml'
            ]
        
        if self.indexing.ignore_patterns is None:
            self.indexing.ignore_patterns = [
                'node_modules', '.git', '.vscode', '__pycache__', '.pytest_cache',
                'dist', 'build', 'target', 'bin', 'obj', '.idea', '.vs',
                'venv', 'env', '.env', '.venv'
            ]

class ConfigManager:
    """Configuration manager for the agent"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = Path(config_path) if config_path else Path.cwd() / ".augura" / "config.json"
        self.config: Optional[AgentConfig] = None
        
        # Ensure config directory exists
        self.config_path.parent.mkdir(parents=True, exist_ok=True)
    
    def load_config(self, project_path: str = None) -> AgentConfig:
        """Load configuration from file or create default"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # Convert nested dictionaries to dataclasses
                config_data['api'] = APIConfig(**config_data.get('api', {}))
                config_data['memory'] = MemoryConfig(**config_data.get('memory', {}))
                config_data['indexing'] = IndexingConfig(**config_data.get('indexing', {}))
                config_data['rag'] = RAGConfig(**config_data.get('rag', {}))
                config_data['mcp'] = MCPConfig(**config_data.get('mcp', {}))
                config_data['logging'] = LoggingConfig(**config_data.get('logging', {}))
                
                self.config = AgentConfig(**config_data)
                print(f"Loaded configuration from {self.config_path}")
            else:
                self.config = self._create_default_config()
                print("Created default configuration")
            
            # Override project path if provided
            if project_path:
                self.config.project_path = project_path
            
            # Load environment variables
            self._load_env_variables()
            
            return self.config
            
        except Exception as e:
            print(f"Error loading configuration: {e}")
            self.config = self._create_default_config()
            if project_path:
                self.config.project_path = project_path
            self._load_env_variables()
            return self.config
    
    def save_config(self) -> bool:
        """Save current configuration to file"""
        if not self.config:
            return False
        
        try:
            config_dict = asdict(self.config)
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2)
            
            print(f"Configuration saved to {self.config_path}")
            return True
            
        except Exception as e:
            print(f"Error saving configuration: {e}")
            return False
    
    def _create_default_config(self) -> AgentConfig:
        """Create default configuration"""
        return AgentConfig(
            api=APIConfig(),
            memory=MemoryConfig(),
            indexing=IndexingConfig(),
            rag=RAGConfig(),
            mcp=MCPConfig(),
            logging=LoggingConfig()
        )
    
    def _load_env_variables(self):
        """Load configuration from environment variables"""
        if not self.config:
            return
        
        # API keys
        if os.getenv('OPENROUTER_API_KEY'):
            self.config.api.openrouter_api_key = os.getenv('OPENROUTER_API_KEY')
        
        if os.getenv('GROQ_API_KEY'):
            self.config.api.groq_api_key = os.getenv('GROQ_API_KEY')
        
        # Model settings
        if os.getenv('DEFAULT_MODEL'):
            self.config.api.default_model = os.getenv('DEFAULT_MODEL')
        
        if os.getenv('GROQ_MODEL'):
            self.config.api.groq_model = os.getenv('GROQ_MODEL')
        
        # Debug mode
        if os.getenv('DEBUG_MODE'):
            self.config.debug_mode = os.getenv('DEBUG_MODE').lower() == 'true'
        
        # Logging level
        if os.getenv('LOG_LEVEL'):
            self.config.logging.level = os.getenv('LOG_LEVEL')
        
        # Memory settings
        if os.getenv('MEMORY_ENABLED'):
            self.config.memory.enabled = os.getenv('MEMORY_ENABLED').lower() == 'true'
        
        # Indexing settings
        if os.getenv('INDEXING_ENABLED'):
            self.config.indexing.enabled = os.getenv('INDEXING_ENABLED').lower() == 'true'
        
        # RAG settings
        if os.getenv('RAG_ENABLED'):
            self.config.rag.enabled = os.getenv('RAG_ENABLED').lower() == 'true'
        
        # MCP settings
        if os.getenv('MCP_ENABLED'):
            self.config.mcp.enabled = os.getenv('MCP_ENABLED').lower() == 'true'
        
        if os.getenv('MCP_PORT'):
            self.config.mcp.port = int(os.getenv('MCP_PORT'))
    
    def update_config(self, updates: Dict[str, Any]) -> bool:
        """Update configuration with new values"""
        if not self.config:
            return False
        
        try:
            # Update nested configurations
            for key, value in updates.items():
                if hasattr(self.config, key):
                    if isinstance(value, dict):
                        # Update nested dataclass
                        nested_config = getattr(self.config, key)
                        for nested_key, nested_value in value.items():
                            if hasattr(nested_config, nested_key):
                                setattr(nested_config, nested_key, nested_value)
                    else:
                        setattr(self.config, key, value)
            
            return True
            
        except Exception as e:
            print(f"Error updating configuration: {e}")
            return False
    
    def validate_config(self) -> tuple[bool, list[str]]:
        """Validate configuration and return errors"""
        errors = []
        
        if not self.config:
            errors.append("No configuration loaded")
            return False, errors
        
        # Validate API keys
        if not self.config.api.openrouter_api_key and not self.config.api.groq_api_key:
            errors.append("At least one API key (OpenRouter or Groq) must be provided")
        
        # Validate paths
        if self.config.project_path and not Path(self.config.project_path).exists():
            errors.append(f"Project path does not exist: {self.config.project_path}")
        
        # Validate memory settings
        if self.config.memory.max_conversations < 1:
            errors.append("max_conversations must be at least 1")
        
        # Validate indexing settings
        if self.config.indexing.max_file_size < 1024:
            errors.append("max_file_size must be at least 1024 bytes")
        
        # Validate RAG settings
        if self.config.rag.max_context_length < 1000:
            errors.append("max_context_length must be at least 1000")
        
        # Validate MCP settings
        if self.config.mcp.port < 1024 or self.config.mcp.port > 65535:
            errors.append("MCP port must be between 1024 and 65535")
        
        return len(errors) == 0, errors
    
    def setup_logging(self):
        """Setup logging based on configuration"""
        if not self.config:
            return
        
        # Create logs directory
        log_file = Path(self.config.logging.log_file)
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=getattr(logging, self.config.logging.level.upper()),
            format=self.config.logging.format,
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        
        # Set up rotating file handler
        from logging.handlers import RotatingFileHandler
        
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=self.config.logging.max_file_size,
            backupCount=self.config.logging.backup_count
        )
        file_handler.setFormatter(logging.Formatter(self.config.logging.format))
        
        # Get root logger and add handler
        logger = logging.getLogger()
        logger.addHandler(file_handler)
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get a summary of current configuration"""
        if not self.config:
            return {}
        
        return {
            "api": {
                "openrouter_configured": bool(self.config.api.openrouter_api_key),
                "groq_configured": bool(self.config.api.groq_api_key),
                "default_model": self.config.api.default_model,
                "groq_model": self.config.api.groq_model
            },
            "memory": {
                "enabled": self.config.memory.enabled,
                "memory_dir": self.config.memory.memory_dir
            },
            "indexing": {
                "enabled": self.config.indexing.enabled,
                "supported_extensions": len(self.config.indexing.supported_extensions)
            },
            "rag": {
                "enabled": self.config.rag.enabled,
                "embedding_model": self.config.rag.embedding_model
            },
            "mcp": {
                "enabled": self.config.mcp.enabled,
                "port": self.config.mcp.port
            },
            "project_path": self.config.project_path,
            "debug_mode": self.config.debug_mode
        }

# Global config manager instance
config_manager = ConfigManager()

def get_config(project_path: str = None) -> AgentConfig:
    """Get the current configuration"""
    return config_manager.load_config(project_path)

def save_config() -> bool:
    """Save the current configuration"""
    return config_manager.save_config()

def update_config(updates: Dict[str, Any]) -> bool:
    """Update configuration with new values"""
    return config_manager.update_config(updates)

if __name__ == "__main__":
    # Test configuration
    config = get_config()
    print("Configuration loaded:")
    print(json.dumps(config_manager.get_config_summary(), indent=2))
    
    # Validate configuration
    is_valid, errors = config_manager.validate_config()
    if not is_valid:
        print("\nConfiguration errors:")
        for error in errors:
            print(f"- {error}")
    else:
        print("\nConfiguration is valid")
