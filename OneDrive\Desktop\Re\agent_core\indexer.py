#!/usr/bin/env python3
"""
Advanced Project Indexer for Augura Coder Agent
Provides deep codebase understanding and intelligent indexing
"""

import os
import sys
import json
import ast
import re
import hashlib
import sqlite3
import threading
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor, as_completed
import argparse

# Try to import optional dependencies
try:
    import tree_sitter
    from tree_sitter import Language, Parser
    TREE_SITTER_AVAILABLE = True
except ImportError:
    TREE_SITTER_AVAILABLE = False
    print("Warning: tree-sitter not available. Using basic parsing.")

try:
    import numpy as np
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("Warning: scikit-learn not available. Advanced similarity features disabled.")

@dataclass
class FileMetadata:
    """Metadata for indexed files"""
    path: str
    relative_path: str
    size: int
    last_modified: float
    language: str
    encoding: str
    line_count: int
    hash: str

@dataclass
class CodeElement:
    """Represents a code element (function, class, variable, etc.)"""
    name: str
    type: str  # 'function', 'class', 'variable', 'import', 'constant'
    line_start: int
    line_end: int
    column_start: int
    column_end: int
    signature: Optional[str] = None
    docstring: Optional[str] = None
    parameters: List[str] = None
    return_type: Optional[str] = None
    decorators: List[str] = None
    parent: Optional[str] = None  # Parent class/function
    visibility: str = 'public'  # 'public', 'private', 'protected'
    complexity: int = 1
    dependencies: List[str] = None

@dataclass
class FileAnalysis:
    """Complete analysis of a file"""
    metadata: FileMetadata
    elements: List[CodeElement]
    imports: List[str]
    exports: List[str]
    dependencies: List[str]
    complexity_metrics: Dict[str, Any]
    issues: List[Dict[str, Any]]
    semantic_chunks: List[Dict[str, Any]]

class LanguageDetector:
    """Detects programming language from file extension and content"""

    LANGUAGE_MAP = {
        '.py': 'python',
        '.js': 'javascript',
        '.jsx': 'javascript',
        '.ts': 'typescript',
        '.tsx': 'typescript',
        '.java': 'java',
        '.cpp': 'cpp',
        '.cc': 'cpp',
        '.cxx': 'cpp',
        '.c': 'c',
        '.h': 'c',
        '.hpp': 'cpp',
        '.cs': 'csharp',
        '.php': 'php',
        '.rb': 'ruby',
        '.go': 'go',
        '.rs': 'rust',
        '.swift': 'swift',
        '.kt': 'kotlin',
        '.scala': 'scala',
        '.html': 'html',
        '.css': 'css',
        '.scss': 'scss',
        '.sass': 'sass',
        '.less': 'less',
        '.json': 'json',
        '.xml': 'xml',
        '.yaml': 'yaml',
        '.yml': 'yaml',
        '.toml': 'toml',
        '.ini': 'ini',
        '.cfg': 'ini',
        '.conf': 'ini',
        '.md': 'markdown',
        '.rst': 'rst',
        '.txt': 'text',
        '.sql': 'sql',
        '.sh': 'bash',
        '.bash': 'bash',
        '.zsh': 'zsh',
        '.fish': 'fish',
        '.ps1': 'powershell',
        '.bat': 'batch',
        '.cmd': 'batch'
    }

    @classmethod
    def detect_language(cls, file_path: str, content: str = None) -> str:
        """Detect language from file extension and optionally content"""
        ext = Path(file_path).suffix.lower()

        # Primary detection by extension
        if ext in cls.LANGUAGE_MAP:
            return cls.LANGUAGE_MAP[ext]

        # Secondary detection by content (if available)
        if content:
            return cls._detect_by_content(content)

        return 'text'

    @classmethod
    def _detect_by_content(cls, content: str) -> str:
        """Detect language by analyzing content"""
        content_lower = content.lower()

        # Check for shebangs
        if content.startswith('#!'):
            first_line = content.split('\n')[0]
            if 'python' in first_line:
                return 'python'
            elif 'node' in first_line or 'javascript' in first_line:
                return 'javascript'
            elif 'bash' in first_line or 'sh' in first_line:
                return 'bash'

        # Check for language-specific patterns
        patterns = {
            'python': [r'def\s+\w+\s*\(', r'import\s+\w+', r'from\s+\w+\s+import', r'class\s+\w+'],
            'javascript': [r'function\s+\w+\s*\(', r'const\s+\w+\s*=', r'let\s+\w+\s*=', r'var\s+\w+\s*='],
            'java': [r'public\s+class\s+\w+', r'public\s+static\s+void\s+main', r'import\s+java\.'],
            'cpp': [r'#include\s*<', r'using\s+namespace', r'int\s+main\s*\('],
            'c': [r'#include\s*<', r'int\s+main\s*\(', r'printf\s*\('],
        }

        for lang, lang_patterns in patterns.items():
            if any(re.search(pattern, content) for pattern in lang_patterns):
                return lang

        return 'text'

class PythonAnalyzer:
    """Specialized analyzer for Python code"""

    def analyze(self, content: str, file_path: str) -> List[CodeElement]:
        """Analyze Python code and extract elements"""
        elements = []

        try:
            tree = ast.parse(content)
            analyzer = PythonASTVisitor(file_path)
            analyzer.visit(tree)
            elements = analyzer.elements
        except SyntaxError as e:
            print(f"Syntax error in {file_path}: {e}")
        except Exception as e:
            print(f"Error analyzing Python file {file_path}: {e}")

        return elements

class PythonASTVisitor(ast.NodeVisitor):
    """AST visitor for Python code analysis"""

    def __init__(self, file_path: str):
        self.file_path = file_path
        self.elements = []
        self.current_class = None
        self.current_function = None
        self.imports = []

    def visit_Import(self, node):
        for alias in node.names:
            self.elements.append(CodeElement(
                name=alias.name,
                type='import',
                line_start=node.lineno,
                line_end=node.lineno,
                column_start=node.col_offset,
                column_end=node.col_offset + len(alias.name),
                signature=f"import {alias.name}"
            ))
        self.generic_visit(node)

    def visit_ImportFrom(self, node):
        module = node.module or ''
        for alias in node.names:
            self.elements.append(CodeElement(
                name=alias.name,
                type='import',
                line_start=node.lineno,
                line_end=node.lineno,
                column_start=node.col_offset,
                column_end=node.col_offset + len(alias.name),
                signature=f"from {module} import {alias.name}"
            ))
        self.generic_visit(node)

    def visit_ClassDef(self, node):
        # Extract class information
        decorators = [d.id if isinstance(d, ast.Name) else str(d) for d in node.decorator_list]
        bases = [base.id if isinstance(base, ast.Name) else str(base) for base in node.bases]

        docstring = None
        if (node.body and isinstance(node.body[0], ast.Expr) and
            isinstance(node.body[0].value, ast.Str)):
            docstring = node.body[0].value.s

        element = CodeElement(
            name=node.name,
            type='class',
            line_start=node.lineno,
            line_end=node.end_lineno or node.lineno,
            column_start=node.col_offset,
            column_end=node.end_col_offset or node.col_offset,
            signature=f"class {node.name}({', '.join(bases)})",
            docstring=docstring,
            decorators=decorators,
            parent=self.current_class
        )

        self.elements.append(element)

        # Visit class body with current class context
        old_class = self.current_class
        self.current_class = node.name
        self.generic_visit(node)
        self.current_class = old_class

    def visit_FunctionDef(self, node):
        # Extract function information
        decorators = [d.id if isinstance(d, ast.Name) else str(d) for d in node.decorator_list]

        # Extract parameters
        parameters = []
        for arg in node.args.args:
            parameters.append(arg.arg)

        # Extract docstring
        docstring = None
        if (node.body and isinstance(node.body[0], ast.Expr) and
            isinstance(node.body[0].value, ast.Str)):
            docstring = node.body[0].value.s

        # Determine visibility
        visibility = 'private' if node.name.startswith('_') else 'public'

        # Calculate complexity (simplified)
        complexity = self._calculate_complexity(node)

        element = CodeElement(
            name=node.name,
            type='function',
            line_start=node.lineno,
            line_end=node.end_lineno or node.lineno,
            column_start=node.col_offset,
            column_end=node.end_col_offset or node.col_offset,
            signature=f"def {node.name}({', '.join(parameters)})",
            docstring=docstring,
            parameters=parameters,
            decorators=decorators,
            parent=self.current_class,
            visibility=visibility,
            complexity=complexity
        )

        self.elements.append(element)

        # Visit function body with current function context
        old_function = self.current_function
        self.current_function = node.name
        self.generic_visit(node)
        self.current_function = old_function

    def visit_Assign(self, node):
        # Extract variable assignments
        for target in node.targets:
            if isinstance(target, ast.Name):
                visibility = 'private' if target.id.startswith('_') else 'public'

                element = CodeElement(
                    name=target.id,
                    type='variable',
                    line_start=node.lineno,
                    line_end=node.lineno,
                    column_start=node.col_offset,
                    column_end=node.col_offset + len(target.id),
                    parent=self.current_class or self.current_function,
                    visibility=visibility
                )

                self.elements.append(element)

        self.generic_visit(node)

    def _calculate_complexity(self, node):
        """Calculate cyclomatic complexity of a function"""
        complexity = 1  # Base complexity

        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
            elif isinstance(child, (ast.And, ast.Or)):
                complexity += 1

        return complexity

class JavaScriptAnalyzer:
    """Specialized analyzer for JavaScript/TypeScript code"""

    def analyze(self, content: str, file_path: str) -> List[CodeElement]:
        """Analyze JavaScript code and extract elements"""
        elements = []
        lines = content.split('\n')

        for i, line in enumerate(lines):
            line_num = i + 1
            stripped = line.strip()

            # Function declarations
            func_match = re.match(r'(?:async\s+)?(?:function\s+(\w+)|(\w+)\s*[:=]\s*(?:async\s+)?(?:function|\([^)]*\)\s*=>))', stripped)
            if func_match:
                func_name = func_match.group(1) or func_match.group(2)
                elements.append(CodeElement(
                    name=func_name,
                    type='function',
                    line_start=line_num,
                    line_end=line_num,
                    column_start=line.find(func_name),
                    column_end=line.find(func_name) + len(func_name),
                    signature=stripped
                ))

            # Class declarations
            class_match = re.match(r'class\s+(\w+)(?:\s+extends\s+(\w+))?', stripped)
            if class_match:
                class_name = class_match.group(1)
                elements.append(CodeElement(
                    name=class_name,
                    type='class',
                    line_start=line_num,
                    line_end=line_num,
                    column_start=line.find(class_name),
                    column_end=line.find(class_name) + len(class_name),
                    signature=stripped
                ))

            # Variable declarations
            var_match = re.match(r'(?:let|const|var)\s+(\w+)', stripped)
            if var_match:
                var_name = var_match.group(1)
                elements.append(CodeElement(
                    name=var_name,
                    type='variable',
                    line_start=line_num,
                    line_end=line_num,
                    column_start=line.find(var_name),
                    column_end=line.find(var_name) + len(var_name),
                    signature=stripped
                ))

            # Import statements
            import_match = re.match(r'import\s+(.+?)\s+from\s+[\'"]([^\'"]+)[\'"]', stripped)
            if import_match:
                imported = import_match.group(1)
                module = import_match.group(2)
                elements.append(CodeElement(
                    name=imported,
                    type='import',
                    line_start=line_num,
                    line_end=line_num,
                    column_start=0,
                    column_end=len(stripped),
                    signature=stripped
                ))

        return elements

class ProjectIndexer:
    """Main project indexer class"""

    def __init__(self, project_path: str, db_path: str = None):
        self.project_path = Path(project_path).resolve()
        if db_path:
            self.db_path = Path(db_path)
        else:
            self.db_path = self.project_path / '.augura' / 'index.db'
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        # Initialize analyzers
        self.analyzers = {
            'python': PythonAnalyzer(),
            'javascript': JavaScriptAnalyzer(),
            'typescript': JavaScriptAnalyzer(),
        }

        # Initialize database
        self.init_database()

        # File patterns to ignore
        self.ignore_patterns = {
            'node_modules', '.git', '.vscode', '__pycache__', '.pytest_cache',
            'dist', 'build', 'target', 'bin', 'obj', '.idea', '.vs',
            'venv', 'env', '.env', '.venv'
        }

        # File extensions to index
        self.indexable_extensions = {
            '.py', '.js', '.jsx', '.ts', '.tsx', '.java', '.cpp', '.c', '.h',
            '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala',
            '.html', '.css', '.scss', '.sass', '.json', '.xml', '.yaml', '.yml'
        }

        # Vector store for semantic search (if available)
        self.vectorizer = None
        self.document_vectors = None
        self.document_paths = []

        if SKLEARN_AVAILABLE:
            self.vectorizer = TfidfVectorizer(
                max_features=10000,
                stop_words='english',
                ngram_range=(1, 2)
            )

    def init_database(self):
        """Initialize SQLite database for storing index data"""
        self.conn = sqlite3.connect(str(self.db_path), check_same_thread=False)
        self.conn.execute('PRAGMA foreign_keys = ON')

        # Create tables
        self.conn.executescript('''
            CREATE TABLE IF NOT EXISTS files (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                path TEXT UNIQUE NOT NULL,
                relative_path TEXT NOT NULL,
                size INTEGER NOT NULL,
                last_modified REAL NOT NULL,
                language TEXT NOT NULL,
                encoding TEXT NOT NULL,
                line_count INTEGER NOT NULL,
                hash TEXT NOT NULL,
                indexed_at REAL NOT NULL
            );

            CREATE TABLE IF NOT EXISTS elements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_id INTEGER NOT NULL,
                name TEXT NOT NULL,
                type TEXT NOT NULL,
                line_start INTEGER NOT NULL,
                line_end INTEGER NOT NULL,
                column_start INTEGER NOT NULL,
                column_end INTEGER NOT NULL,
                signature TEXT,
                docstring TEXT,
                parameters TEXT,
                return_type TEXT,
                decorators TEXT,
                parent TEXT,
                visibility TEXT DEFAULT 'public',
                complexity INTEGER DEFAULT 1,
                FOREIGN KEY (file_id) REFERENCES files (id) ON DELETE CASCADE
            );

            CREATE TABLE IF NOT EXISTS dependencies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_id INTEGER NOT NULL,
                dependency_path TEXT NOT NULL,
                dependency_type TEXT NOT NULL,
                FOREIGN KEY (file_id) REFERENCES files (id) ON DELETE CASCADE
            );

            CREATE TABLE IF NOT EXISTS semantic_chunks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_id INTEGER NOT NULL,
                chunk_index INTEGER NOT NULL,
                content TEXT NOT NULL,
                start_line INTEGER NOT NULL,
                end_line INTEGER NOT NULL,
                embedding BLOB,
                FOREIGN KEY (file_id) REFERENCES files (id) ON DELETE CASCADE
            );

            CREATE INDEX IF NOT EXISTS idx_files_path ON files (path);
            CREATE INDEX IF NOT EXISTS idx_elements_name ON elements (name);
            CREATE INDEX IF NOT EXISTS idx_elements_type ON elements (type);
            CREATE INDEX IF NOT EXISTS idx_elements_file_id ON elements (file_id);
            CREATE INDEX IF NOT EXISTS idx_dependencies_file_id ON dependencies (file_id);
            CREATE INDEX IF NOT EXISTS idx_semantic_chunks_file_id ON semantic_chunks (file_id);
        ''')

        self.conn.commit()

    def should_ignore_path(self, path: Path) -> bool:
        """Check if a path should be ignored during indexing"""
        path_parts = path.parts

        # Check if any part of the path matches ignore patterns
        for part in path_parts:
            if part in self.ignore_patterns or part.startswith('.'):
                return True

        # Check file extension
        if path.is_file() and path.suffix not in self.indexable_extensions:
            return True

        return False

    def get_file_hash(self, content: str) -> str:
        """Calculate hash of file content"""
        return hashlib.sha256(content.encode('utf-8')).hexdigest()

    def detect_encoding(self, file_path: Path) -> str:
        """Detect file encoding"""
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read(1024)

            # Try common encodings
            for encoding in ['utf-8', 'utf-16', 'latin-1', 'cp1252']:
                try:
                    raw_data.decode(encoding)
                    return encoding
                except UnicodeDecodeError:
                    continue

            return 'utf-8'  # Default fallback
        except Exception:
            return 'utf-8'

    def read_file_content(self, file_path: Path) -> Tuple[str, str]:
        """Read file content and detect encoding"""
        encoding = self.detect_encoding(file_path)

        try:
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
            return content, encoding
        except Exception as e:
            print(f"Error reading file {file_path}: {e}")
            return "", encoding

    def analyze_file(self, file_path: Path) -> Optional[FileAnalysis]:
        """Analyze a single file and extract all information"""
        try:
            # Read file content
            content, encoding = self.read_file_content(file_path)
            if not content:
                return None

            # Get file stats
            stat = file_path.stat()
            relative_path = str(file_path.relative_to(self.project_path))

            # Create metadata
            metadata = FileMetadata(
                path=str(file_path),
                relative_path=relative_path,
                size=stat.st_size,
                last_modified=stat.st_mtime,
                language=LanguageDetector.detect_language(str(file_path), content),
                encoding=encoding,
                line_count=len(content.split('\n')),
                hash=self.get_file_hash(content)
            )

            # Analyze code elements
            elements = []
            if metadata.language in self.analyzers:
                analyzer = self.analyzers[metadata.language]
                elements = analyzer.analyze(content, str(file_path))

            # Extract imports and dependencies
            imports, dependencies = self.extract_dependencies(content, metadata.language)

            # Calculate complexity metrics
            complexity_metrics = self.calculate_complexity_metrics(content, elements)

            # Detect issues
            issues = self.detect_issues(content, metadata.language)

            # Create semantic chunks
            semantic_chunks = self.create_semantic_chunks(content, elements)

            return FileAnalysis(
                metadata=metadata,
                elements=elements,
                imports=imports,
                exports=[],  # TODO: Implement export detection
                dependencies=dependencies,
                complexity_metrics=complexity_metrics,
                issues=issues,
                semantic_chunks=semantic_chunks
            )

        except Exception as e:
            print(f"Error analyzing file {file_path}: {e}")
            return None

    def extract_dependencies(self, content: str, language: str) -> Tuple[List[str], List[str]]:
        """Extract imports and dependencies from file content"""
        imports = []
        dependencies = []

        if language == 'python':
            for line in content.split('\n'):
                line = line.strip()
                if line.startswith('import '):
                    module = line.replace('import ', '').split()[0]
                    imports.append(module)
                    dependencies.append(module)
                elif line.startswith('from '):
                    match = re.match(r'from\s+(\S+)\s+import', line)
                    if match:
                        module = match.group(1)
                        imports.append(module)
                        dependencies.append(module)

        elif language in ['javascript', 'typescript']:
            for line in content.split('\n'):
                line = line.strip()
                # ES6 imports
                import_match = re.match(r'import\s+.+?\s+from\s+[\'"]([^\'"]+)[\'"]', line)
                if import_match:
                    module = import_match.group(1)
                    imports.append(module)
                    dependencies.append(module)

                # CommonJS requires
                require_match = re.search(r'require\([\'"]([^\'"]+)[\'"]\)', line)
                if require_match:
                    module = require_match.group(1)
                    imports.append(module)
                    dependencies.append(module)

        return imports, dependencies

    def calculate_complexity_metrics(self, content: str, elements: List[CodeElement]) -> Dict[str, Any]:
        """Calculate various complexity metrics"""
        lines = content.split('\n')

        metrics = {
            'total_lines': len(lines),
            'code_lines': len([line for line in lines if line.strip() and not line.strip().startswith('#')]),
            'comment_lines': len([line for line in lines if line.strip().startswith('#')]),
            'blank_lines': len([line for line in lines if not line.strip()]),
            'function_count': len([e for e in elements if e.type == 'function']),
            'class_count': len([e for e in elements if e.type == 'class']),
            'average_function_complexity': 0,
            'max_function_complexity': 0,
            'cyclomatic_complexity': 1
        }

        # Calculate function complexity statistics
        function_complexities = [e.complexity for e in elements if e.type == 'function' and e.complexity]
        if function_complexities:
            metrics['average_function_complexity'] = sum(function_complexities) / len(function_complexities)
            metrics['max_function_complexity'] = max(function_complexities)

        # Calculate overall cyclomatic complexity
        complexity_patterns = [
            r'\bif\b', r'\belse\b', r'\belif\b', r'\bfor\b', r'\bwhile\b',
            r'\btry\b', r'\bcatch\b', r'\bexcept\b', r'\bswitch\b', r'\bcase\b',
            r'\b&&\b', r'\b\|\|\b', r'\band\b', r'\bor\b'
        ]

        for pattern in complexity_patterns:
            metrics['cyclomatic_complexity'] += len(re.findall(pattern, content, re.IGNORECASE))

        return metrics

    def detect_issues(self, content: str, language: str) -> List[Dict[str, Any]]:
        """Detect potential issues in the code"""
        issues = []
        lines = content.split('\n')

        for i, line in enumerate(lines):
            line_num = i + 1

            # Long lines
            if len(line) > 120:
                issues.append({
                    'type': 'style',
                    'severity': 'warning',
                    'line': line_num,
                    'message': 'Line too long (>120 characters)',
                    'suggestion': 'Consider breaking this line'
                })

            # TODO comments
            if 'TODO' in line or 'FIXME' in line:
                issues.append({
                    'type': 'todo',
                    'severity': 'info',
                    'line': line_num,
                    'message': 'TODO/FIXME comment found',
                    'suggestion': 'Consider addressing this item'
                })

            # Language-specific issues
            if language == 'python':
                if 'print(' in line and not line.strip().startswith('#'):
                    issues.append({
                        'type': 'debug',
                        'severity': 'warning',
                        'line': line_num,
                        'message': 'print statement found',
                        'suggestion': 'Consider using logging instead'
                    })

            elif language in ['javascript', 'typescript']:
                if 'console.log' in line:
                    issues.append({
                        'type': 'debug',
                        'severity': 'warning',
                        'line': line_num,
                        'message': 'console.log statement found',
                        'suggestion': 'Remove debug statements before production'
                    })

                if '==' in line and '===' not in line:
                    issues.append({
                        'type': 'style',
                        'severity': 'warning',
                        'line': line_num,
                        'message': 'Use === instead of ==',
                        'suggestion': 'Use strict equality for better type safety'
                    })

        return issues

    def create_semantic_chunks(self, content: str, elements: List[CodeElement]) -> List[Dict[str, Any]]:
        """Create semantic chunks for better context understanding"""
        chunks = []
        lines = content.split('\n')

        # Create chunks based on code elements
        for element in elements:
            if element.type in ['function', 'class']:
                start_line = max(0, element.line_start - 1)
                end_line = min(len(lines), element.line_end)

                chunk_content = '\n'.join(lines[start_line:end_line])

                chunks.append({
                    'content': chunk_content,
                    'start_line': start_line + 1,
                    'end_line': end_line,
                    'element_name': element.name,
                    'element_type': element.type,
                    'context': f"{element.type} {element.name}"
                })

        # Create chunks for remaining content
        covered_lines = set()
        for chunk in chunks:
            covered_lines.update(range(chunk['start_line'], chunk['end_line'] + 1))

        current_chunk = []
        chunk_start = 1

        for i, line in enumerate(lines):
            line_num = i + 1

            if line_num not in covered_lines:
                current_chunk.append(line)
            else:
                if current_chunk:
                    chunks.append({
                        'content': '\n'.join(current_chunk),
                        'start_line': chunk_start,
                        'end_line': line_num - 1,
                        'element_name': None,
                        'element_type': 'code_block',
                        'context': 'general code'
                    })
                    current_chunk = []
                chunk_start = line_num + 1

        # Add final chunk if exists
        if current_chunk:
            chunks.append({
                'content': '\n'.join(current_chunk),
                'start_line': chunk_start,
                'end_line': len(lines),
                'element_name': None,
                'element_type': 'code_block',
                'context': 'general code'
            })

        return chunks

    def store_file_analysis(self, analysis: FileAnalysis):
        """Store file analysis in database"""
        cursor = self.conn.cursor()

        try:
            # Insert or update file record
            cursor.execute('''
                INSERT OR REPLACE INTO files
                (path, relative_path, size, last_modified, language, encoding, line_count, hash, indexed_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                analysis.metadata.path,
                analysis.metadata.relative_path,
                analysis.metadata.size,
                analysis.metadata.last_modified,
                analysis.metadata.language,
                analysis.metadata.encoding,
                analysis.metadata.line_count,
                analysis.metadata.hash,
                time.time()
            ))

            file_id = cursor.lastrowid

            # Clear existing elements and dependencies
            cursor.execute('DELETE FROM elements WHERE file_id = ?', (file_id,))
            cursor.execute('DELETE FROM dependencies WHERE file_id = ?', (file_id,))
            cursor.execute('DELETE FROM semantic_chunks WHERE file_id = ?', (file_id,))

            # Insert elements
            for element in analysis.elements:
                cursor.execute('''
                    INSERT INTO elements
                    (file_id, name, type, line_start, line_end, column_start, column_end,
                     signature, docstring, parameters, return_type, decorators, parent, visibility, complexity)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    file_id, element.name, element.type, element.line_start, element.line_end,
                    element.column_start, element.column_end, element.signature, element.docstring,
                    json.dumps(element.parameters) if element.parameters else None,
                    element.return_type,
                    json.dumps(element.decorators) if element.decorators else None,
                    element.parent, element.visibility, element.complexity
                ))

            # Insert dependencies
            for dep in analysis.dependencies:
                cursor.execute('''
                    INSERT INTO dependencies (file_id, dependency_path, dependency_type)
                    VALUES (?, ?, ?)
                ''', (file_id, dep, 'import'))

            # Insert semantic chunks
            for i, chunk in enumerate(analysis.semantic_chunks):
                cursor.execute('''
                    INSERT INTO semantic_chunks
                    (file_id, chunk_index, content, start_line, end_line)
                    VALUES (?, ?, ?, ?, ?)
                ''', (file_id, i, chunk['content'], chunk['start_line'], chunk['end_line']))

            self.conn.commit()

        except Exception as e:
            print(f"Error storing file analysis: {e}")
            self.conn.rollback()

    def index_project(self, max_workers: int = 4):
        """Index the entire project"""
        print(f"Starting indexing of project: {self.project_path}")

        # Find all files to index
        files_to_index = []
        for file_path in self.project_path.rglob('*'):
            if file_path.is_file() and not self.should_ignore_path(file_path):
                files_to_index.append(file_path)

        print(f"Found {len(files_to_index)} files to index")

        # Index files in parallel
        indexed_count = 0
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_file = {
                executor.submit(self.analyze_file, file_path): file_path
                for file_path in files_to_index
            }

            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    analysis = future.result()
                    if analysis:
                        self.store_file_analysis(analysis)
                        indexed_count += 1

                        if indexed_count % 10 == 0:
                            print(f"Indexed {indexed_count}/{len(files_to_index)} files")

                except Exception as e:
                    print(f"Error indexing {file_path}: {e}")

        print(f"Indexing complete. Indexed {indexed_count} files.")

        # Build vector index if available
        if SKLEARN_AVAILABLE:
            self.build_vector_index()

    def build_vector_index(self):
        """Build vector index for semantic search"""
        print("Building vector index for semantic search...")

        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT f.path, sc.content
            FROM files f
            JOIN semantic_chunks sc ON f.id = sc.file_id
        ''')

        documents = []
        paths = []

        for path, content in cursor.fetchall():
            documents.append(content)
            paths.append(path)

        if documents:
            try:
                self.document_vectors = self.vectorizer.fit_transform(documents)
                self.document_paths = paths
                print(f"Built vector index with {len(documents)} documents")
            except Exception as e:
                print(f"Error building vector index: {e}")

    def search_semantic(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Perform semantic search using vector similarity"""
        if not SKLEARN_AVAILABLE or self.document_vectors is None:
            return []

        try:
            query_vector = self.vectorizer.transform([query])
            similarities = cosine_similarity(query_vector, self.document_vectors).flatten()

            # Get top results
            top_indices = similarities.argsort()[-limit:][::-1]

            results = []
            for idx in top_indices:
                if similarities[idx] > 0.1:  # Minimum similarity threshold
                    results.append({
                        'path': self.document_paths[idx],
                        'similarity': float(similarities[idx]),
                        'type': 'semantic'
                    })

            return results

        except Exception as e:
            print(f"Error in semantic search: {e}")
            return []

    def search_code(self, query: str, search_type: str = 'all') -> List[Dict[str, Any]]:
        """Search for code elements"""
        cursor = self.conn.cursor()
        results = []

        # Text-based search in elements
        if search_type in ['all', 'elements']:
            cursor.execute('''
                SELECT f.path, f.relative_path, e.name, e.type, e.line_start, e.signature
                FROM files f
                JOIN elements e ON f.id = e.file_id
                WHERE e.name LIKE ? OR e.signature LIKE ?
                ORDER BY e.name
            ''', (f'%{query}%', f'%{query}%'))

            for row in cursor.fetchall():
                results.append({
                    'path': row[0],
                    'relative_path': row[1],
                    'element_name': row[2],
                    'element_type': row[3],
                    'line': row[4],
                    'signature': row[5],
                    'type': 'element'
                })

        # File name search
        if search_type in ['all', 'files']:
            cursor.execute('''
                SELECT path, relative_path, language
                FROM files
                WHERE relative_path LIKE ?
                ORDER BY relative_path
            ''', (f'%{query}%',))

            for row in cursor.fetchall():
                results.append({
                    'path': row[0],
                    'relative_path': row[1],
                    'language': row[2],
                    'type': 'file'
                })

        # Semantic search (if available)
        if search_type in ['all', 'semantic']:
            semantic_results = self.search_semantic(query)
            results.extend(semantic_results)

        return results

    def get_file_info(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific file"""
        cursor = self.conn.cursor()

        # Get file metadata
        cursor.execute('SELECT * FROM files WHERE path = ?', (file_path,))
        file_row = cursor.fetchone()

        if not file_row:
            return None

        file_id = file_row[0]

        # Get elements
        cursor.execute('''
            SELECT name, type, line_start, line_end, signature, docstring, complexity
            FROM elements
            WHERE file_id = ?
            ORDER BY line_start
        ''', (file_id,))

        elements = []
        for row in cursor.fetchall():
            elements.append({
                'name': row[0],
                'type': row[1],
                'line_start': row[2],
                'line_end': row[3],
                'signature': row[4],
                'docstring': row[5],
                'complexity': row[6]
            })

        # Get dependencies
        cursor.execute('''
            SELECT dependency_path, dependency_type
            FROM dependencies
            WHERE file_id = ?
        ''', (file_id,))

        dependencies = [{'path': row[0], 'type': row[1]} for row in cursor.fetchall()]

        return {
            'metadata': {
                'path': file_row[1],
                'relative_path': file_row[2],
                'size': file_row[3],
                'last_modified': file_row[4],
                'language': file_row[5],
                'line_count': file_row[7]
            },
            'elements': elements,
            'dependencies': dependencies
        }

    def get_project_stats(self) -> Dict[str, Any]:
        """Get project statistics"""
        cursor = self.conn.cursor()

        # File statistics
        cursor.execute('SELECT COUNT(*), SUM(size), SUM(line_count) FROM files')
        file_stats = cursor.fetchone()

        # Language distribution
        cursor.execute('SELECT language, COUNT(*) FROM files GROUP BY language ORDER BY COUNT(*) DESC')
        language_dist = dict(cursor.fetchall())

        # Element statistics
        cursor.execute('SELECT type, COUNT(*) FROM elements GROUP BY type ORDER BY COUNT(*) DESC')
        element_dist = dict(cursor.fetchall())

        # Complexity statistics
        cursor.execute('SELECT AVG(complexity), MAX(complexity) FROM elements WHERE type = "function"')
        complexity_stats = cursor.fetchone()

        return {
            'total_files': file_stats[0] or 0,
            'total_size': file_stats[1] or 0,
            'total_lines': file_stats[2] or 0,
            'language_distribution': language_dist,
            'element_distribution': element_dist,
            'average_function_complexity': complexity_stats[0] or 0,
            'max_function_complexity': complexity_stats[1] or 0
        }

    def close(self):
        """Close database connection"""
        if hasattr(self, 'conn'):
            self.conn.close()

def main():
    """Main entry point for command-line usage"""
    parser = argparse.ArgumentParser(description='Augura Project Indexer')
    parser.add_argument('project_path', help='Path to the project to index')
    parser.add_argument('--db-path', help='Path to the database file')
    parser.add_argument('--workers', type=int, default=4, help='Number of worker threads')
    parser.add_argument('--search', help='Search query to test')
    parser.add_argument('--stats', action='store_true', help='Show project statistics')

    args = parser.parse_args()

    # Initialize indexer
    indexer = ProjectIndexer(args.project_path, args.db_path)

    try:
        if args.search:
            # Perform search
            results = indexer.search_code(args.search)
            print(f"Search results for '{args.search}':")
            for result in results[:10]:
                print(f"  {result}")

        elif args.stats:
            # Show statistics
            stats = indexer.get_project_stats()
            print("Project Statistics:")
            for key, value in stats.items():
                print(f"  {key}: {value}")

        else:
            # Index the project
            indexer.index_project(args.workers)

    finally:
        indexer.close()

if __name__ == '__main__':
    main()
