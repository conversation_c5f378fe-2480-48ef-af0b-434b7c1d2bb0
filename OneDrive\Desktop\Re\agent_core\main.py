#!/usr/bin/env python3
"""
Main entry point for Augura Coder Agent
"""

import sys
import os
import asyncio
import argparse
import json
import signal
from pathlib import Path
from typing import Optional

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import ConfigManager, get_config
from ai_agent import AIAgent, AgentBridge, AgentConfig as AIAgentConfig
from mcp_integration import start_mcp_server
import logging

class AuguraAgent:
    """Main Augura Coder Agent application"""
    
    def __init__(self, project_path: str, config_path: Optional[str] = None):
        self.project_path = Path(project_path).resolve()
        self.config_manager = ConfigManager(config_path)
        self.config = None
        self.ai_agent = None
        self.agent_bridge = None
        self.mcp_server = None
        self.is_running = False
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print(f"\nReceived signal {signum}, shutting down...")
        asyncio.create_task(self.shutdown())
    
    async def initialize(self):
        """Initialize the agent"""
        try:
            print("🚀 Initializing Augura Coder Agent...")
            
            # Load configuration
            self.config = self.config_manager.load_config(str(self.project_path))
            
            # Setup logging
            self.config_manager.setup_logging()
            logger = logging.getLogger(__name__)
            logger.info("Starting Augura Coder Agent")
            
            # Validate configuration
            is_valid, errors = self.config_manager.validate_config()
            if not is_valid:
                print("❌ Configuration validation failed:")
                for error in errors:
                    print(f"   - {error}")
                return False
            
            print("✅ Configuration validated")
            
            # Create AI agent configuration
            ai_config = AIAgentConfig(
                openrouter_api_key=self.config.api.openrouter_api_key,
                groq_api_key=self.config.api.groq_api_key,
                default_model=self.config.api.default_model,
                groq_model=self.config.api.groq_model,
                max_tokens=self.config.api.max_tokens,
                temperature=self.config.api.temperature,
                memory_dir=self.config.memory.memory_dir,
                index_db=self.config.indexing.index_db_path,
                enable_learning=self.config.memory.enabled,
                enable_memory=self.config.memory.enabled,
                enable_rag=self.config.rag.enabled
            )
            
            # Initialize AI agent
            self.ai_agent = AIAgent(ai_config, str(self.project_path))
            await self.ai_agent.initialize()
            print("✅ AI Agent initialized")
            
            # Create agent bridge for synchronous access
            self.agent_bridge = AgentBridge(ai_config, str(self.project_path))
            self.agent_bridge.start()
            print("✅ Agent Bridge started")
            
            # Start MCP server if enabled
            if self.config.mcp.enabled:
                self.mcp_server = start_mcp_server(
                    str(self.project_path), 
                    self.config.mcp.port
                )
                print(f"✅ MCP Server started on port {self.config.mcp.port}")
            
            self.is_running = True
            print("🎉 Augura Coder Agent is ready!")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to initialize agent: {e}")
            logging.error(f"Initialization failed: {e}", exc_info=True)
            return False
    
    async def run_interactive(self):
        """Run in interactive mode"""
        print("\n" + "="*60)
        print("🤖 AUGURA CODER AGENT - INTERACTIVE MODE")
        print("="*60)
        print("Commands:")
        print("  help     - Show this help")
        print("  stats    - Show agent statistics")
        print("  config   - Show configuration summary")
        print("  index    - Reindex the project")
        print("  clear    - Clear conversation history")
        print("  quit     - Exit the agent")
        print("="*60)
        
        while self.is_running:
            try:
                user_input = input("\n🧑 You: ").strip()
                
                if not user_input:
                    continue
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    break
                
                elif user_input.lower() == 'help':
                    self._show_help()
                
                elif user_input.lower() == 'stats':
                    await self._show_stats()
                
                elif user_input.lower() == 'config':
                    self._show_config()
                
                elif user_input.lower() == 'index':
                    await self._reindex_project()
                
                elif user_input.lower() == 'clear':
                    self._clear_history()
                
                else:
                    # Process with AI agent
                    print("🤖 Augura: ", end="", flush=True)
                    
                    # Use streaming response
                    response_text = ""
                    async for chunk in self.ai_agent.process_query_stream(user_input):
                        print(chunk, end="", flush=True)
                        response_text += chunk
                    
                    print()  # New line after response
                
            except KeyboardInterrupt:
                break
            except EOFError:
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                logging.error(f"Interactive mode error: {e}", exc_info=True)
        
        await self.shutdown()
    
    async def process_query(self, query: str, context: dict = None) -> dict:
        """Process a single query"""
        if not self.is_running:
            return {"error": "Agent not running"}
        
        try:
            response = await self.ai_agent.process_query(query, context)
            return {
                "content": response.content,
                "actions": response.actions,
                "suggestions": response.suggestions,
                "confidence": response.confidence,
                "reasoning": response.reasoning,
                "execution_time": response.execution_time
            }
        except Exception as e:
            logging.error(f"Query processing error: {e}", exc_info=True)
            return {"error": str(e)}
    
    def _show_help(self):
        """Show help information"""
        print("\n📚 AUGURA CODER AGENT HELP")
        print("-" * 40)
        print("I'm an advanced AI programming assistant that can:")
        print("• 🔍 Analyze and understand your codebase")
        print("• ✏️  Make intelligent code modifications")
        print("• 🔄 Perform smart refactoring")
        print("• 📁 Handle file operations")
        print("• 🐛 Detect and fix bugs")
        print("• 🧠 Learn from your preferences")
        print("\nJust ask me anything about your code!")
    
    async def _show_stats(self):
        """Show agent statistics"""
        try:
            stats = self.ai_agent.get_stats()
            print("\n📊 AGENT STATISTICS")
            print("-" * 30)
            print(f"Queries Processed: {stats.get('queries_processed', 0)}")
            print(f"Actions Executed: {stats.get('actions_executed', 0)}")
            print(f"Errors Encountered: {stats.get('errors_encountered', 0)}")
            print(f"Average Response Time: {stats.get('average_response_time', 0):.2f}s")
            
            if 'memory' in stats:
                memory_stats = stats['memory']
                print(f"\nMemory:")
                print(f"  Conversations: {memory_stats.get('conversations', 0)}")
                print(f"  Code Interactions: {memory_stats.get('by_type', {}).get('code', 0)}")
                print(f"  Learning Patterns: {memory_stats.get('by_type', {}).get('learning', 0)}")
            
            if 'project' in stats:
                project_stats = stats['project']
                print(f"\nProject:")
                print(f"  Total Files: {project_stats.get('total_files', 0)}")
                print(f"  Total Lines: {project_stats.get('total_lines', 0)}")
                print(f"  Languages: {', '.join(project_stats.get('language_distribution', {}).keys())}")
        
        except Exception as e:
            print(f"❌ Error getting stats: {e}")
    
    def _show_config(self):
        """Show configuration summary"""
        summary = self.config_manager.get_config_summary()
        print("\n⚙️  CONFIGURATION SUMMARY")
        print("-" * 35)
        print(json.dumps(summary, indent=2))
    
    async def _reindex_project(self):
        """Reindex the project"""
        try:
            print("🔄 Reindexing project...")
            if self.ai_agent.indexer:
                await self.ai_agent._run_in_thread(self.ai_agent.indexer.index_project)
                print("✅ Project reindexed successfully")
            else:
                print("❌ Indexer not available")
        except Exception as e:
            print(f"❌ Error reindexing: {e}")
    
    def _clear_history(self):
        """Clear conversation history"""
        try:
            if self.ai_agent:
                self.ai_agent.conversation_history.clear()
                print("✅ Conversation history cleared")
        except Exception as e:
            print(f"❌ Error clearing history: {e}")
    
    async def shutdown(self):
        """Shutdown the agent"""
        if not self.is_running:
            return
        
        print("\n🛑 Shutting down Augura Coder Agent...")
        self.is_running = False
        
        try:
            # Stop AI agent
            if self.ai_agent:
                await self.ai_agent.cleanup()
                print("✅ AI Agent stopped")
            
            # Stop agent bridge
            if self.agent_bridge:
                self.agent_bridge.stop()
                print("✅ Agent Bridge stopped")
            
            # Stop MCP server
            if self.mcp_server:
                self.mcp_server.stop_server()
                print("✅ MCP Server stopped")
            
            print("👋 Goodbye!")
            
        except Exception as e:
            print(f"❌ Error during shutdown: {e}")
            logging.error(f"Shutdown error: {e}", exc_info=True)

async def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Augura Coder Agent - Advanced AI Programming Assistant",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py /path/to/project --interactive
  python main.py /path/to/project --query "Analyze this codebase"
  python main.py /path/to/project --config custom_config.json
        """
    )
    
    parser.add_argument(
        "project_path",
        help="Path to the project directory"
    )
    
    parser.add_argument(
        "--config",
        help="Path to configuration file"
    )
    
    parser.add_argument(
        "--interactive", "-i",
        action="store_true",
        help="Run in interactive mode"
    )
    
    parser.add_argument(
        "--query", "-q",
        help="Process a single query and exit"
    )
    
    parser.add_argument(
        "--stats",
        action="store_true",
        help="Show agent statistics and exit"
    )
    
    parser.add_argument(
        "--validate-config",
        action="store_true",
        help="Validate configuration and exit"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug mode"
    )
    
    args = parser.parse_args()
    
    # Validate project path
    if not Path(args.project_path).exists():
        print(f"❌ Project path does not exist: {args.project_path}")
        return 1
    
    # Initialize agent
    agent = AuguraAgent(args.project_path, args.config)
    
    # Handle debug mode
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Validate configuration only
    if args.validate_config:
        config = agent.config_manager.load_config(args.project_path)
        is_valid, errors = agent.config_manager.validate_config()
        
        if is_valid:
            print("✅ Configuration is valid")
            return 0
        else:
            print("❌ Configuration validation failed:")
            for error in errors:
                print(f"   - {error}")
            return 1
    
    # Initialize agent
    if not await agent.initialize():
        return 1
    
    try:
        # Handle different modes
        if args.stats:
            await agent._show_stats()
        
        elif args.query:
            print(f"Processing query: {args.query}")
            result = await agent.process_query(args.query)
            
            if "error" in result:
                print(f"❌ Error: {result['error']}")
                return 1
            else:
                print(f"\n🤖 Response:\n{result['content']}")
                print(f"\n📊 Confidence: {result['confidence']:.2f}")
                print(f"⏱️  Execution time: {result['execution_time']:.2f}s")
        
        elif args.interactive:
            await agent.run_interactive()
        
        else:
            print("No mode specified. Use --interactive, --query, or --stats")
            return 1
    
    except KeyboardInterrupt:
        print("\n👋 Interrupted by user")
    
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        logging.error(f"Main error: {e}", exc_info=True)
        return 1
    
    finally:
        await agent.shutdown()
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
