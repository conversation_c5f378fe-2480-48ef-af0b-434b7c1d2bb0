#!/usr/bin/env python3
"""
Model Context Protocol (MCP) Integration for Augura Coder Agent
Provides standardized communication between AI models and development tools
"""

import os
import sys
import json
import asyncio
import websockets
import threading
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from pathlib import Path
import time
import uuid

@dataclass
class MCPMessage:
    """Base MCP message structure"""
    id: str
    method: str
    params: Dict[str, Any]
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()

@dataclass
class MCPResponse:
    """MCP response structure"""
    id: str
    result: Any = None
    error: Optional[Dict[str, Any]] = None
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()

@dataclass
class MCPResource:
    """MCP resource definition"""
    uri: str
    name: str
    description: str
    mime_type: str
    metadata: Dict[str, Any] = None

@dataclass
class MCPTool:
    """MCP tool definition"""
    name: str
    description: str
    input_schema: Dict[str, Any]
    handler: Callable = None

class MCPServer:
    """MCP Server implementation for Augura Coder Agent"""
    
    def __init__(self, project_path: str, port: int = 8765):
        self.project_path = Path(project_path)
        self.port = port
        self.server = None
        self.clients = set()
        self.resources = {}
        self.tools = {}
        self.is_running = False
        
        # Initialize built-in tools and resources
        self._initialize_tools()
        self._initialize_resources()
    
    def _initialize_tools(self):
        """Initialize built-in MCP tools"""
        
        # File operations tool
        self.register_tool(MCPTool(
            name="file_operations",
            description="Perform file operations (read, write, create, delete)",
            input_schema={
                "type": "object",
                "properties": {
                    "operation": {
                        "type": "string",
                        "enum": ["read", "write", "create", "delete", "list"]
                    },
                    "path": {"type": "string"},
                    "content": {"type": "string"},
                    "encoding": {"type": "string", "default": "utf-8"}
                },
                "required": ["operation", "path"]
            },
            handler=self._handle_file_operations
        ))
        
        # Code analysis tool
        self.register_tool(MCPTool(
            name="code_analysis",
            description="Analyze code structure and quality",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string"},
                    "analysis_type": {
                        "type": "string",
                        "enum": ["structure", "complexity", "dependencies", "issues"]
                    }
                },
                "required": ["file_path"]
            },
            handler=self._handle_code_analysis
        ))
        
        # Project search tool
        self.register_tool(MCPTool(
            name="project_search",
            description="Search through project files and code",
            input_schema={
                "type": "object",
                "properties": {
                    "query": {"type": "string"},
                    "search_type": {
                        "type": "string",
                        "enum": ["text", "code", "files", "semantic"]
                    },
                    "file_types": {"type": "array", "items": {"type": "string"}},
                    "limit": {"type": "integer", "default": 10}
                },
                "required": ["query"]
            },
            handler=self._handle_project_search
        ))
        
        # Git operations tool
        self.register_tool(MCPTool(
            name="git_operations",
            description="Perform Git version control operations",
            input_schema={
                "type": "object",
                "properties": {
                    "operation": {
                        "type": "string",
                        "enum": ["status", "diff", "log", "branch", "commit", "push", "pull"]
                    },
                    "args": {"type": "array", "items": {"type": "string"}},
                    "message": {"type": "string"}
                },
                "required": ["operation"]
            },
            handler=self._handle_git_operations
        ))
        
        # Terminal execution tool
        self.register_tool(MCPTool(
            name="terminal_execution",
            description="Execute terminal commands",
            input_schema={
                "type": "object",
                "properties": {
                    "command": {"type": "string"},
                    "working_directory": {"type": "string"},
                    "timeout": {"type": "integer", "default": 30},
                    "capture_output": {"type": "boolean", "default": True}
                },
                "required": ["command"]
            },
            handler=self._handle_terminal_execution
        ))
    
    def _initialize_resources(self):
        """Initialize built-in MCP resources"""
        
        # Project structure resource
        self.register_resource(MCPResource(
            uri="project://structure",
            name="Project Structure",
            description="Complete project file structure and organization",
            mime_type="application/json"
        ))
        
        # Project configuration resource
        self.register_resource(MCPResource(
            uri="project://config",
            name="Project Configuration",
            description="Project configuration files and settings",
            mime_type="application/json"
        ))
        
        # Code metrics resource
        self.register_resource(MCPResource(
            uri="project://metrics",
            name="Code Metrics",
            description="Project-wide code quality and complexity metrics",
            mime_type="application/json"
        ))
        
        # Dependencies resource
        self.register_resource(MCPResource(
            uri="project://dependencies",
            name="Project Dependencies",
            description="Project dependencies and their relationships",
            mime_type="application/json"
        ))
    
    def register_tool(self, tool: MCPTool):
        """Register a new MCP tool"""
        self.tools[tool.name] = tool
        print(f"Registered MCP tool: {tool.name}")
    
    def register_resource(self, resource: MCPResource):
        """Register a new MCP resource"""
        self.resources[resource.uri] = resource
        print(f"Registered MCP resource: {resource.uri}")
    
    async def start_server(self):
        """Start the MCP server"""
        try:
            self.server = await websockets.serve(
                self.handle_client,
                "localhost",
                self.port
            )
            self.is_running = True
            print(f"MCP Server started on ws://localhost:{self.port}")
            
            # Keep server running
            await self.server.wait_closed()
            
        except Exception as e:
            print(f"Error starting MCP server: {e}")
            self.is_running = False
    
    async def handle_client(self, websocket, path):
        """Handle client connections"""
        client_id = str(uuid.uuid4())
        self.clients.add(websocket)
        print(f"MCP client connected: {client_id}")
        
        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    response = await self.process_message(data)
                    await websocket.send(json.dumps(asdict(response)))
                    
                except json.JSONDecodeError:
                    error_response = MCPResponse(
                        id="unknown",
                        error={"code": -32700, "message": "Parse error"}
                    )
                    await websocket.send(json.dumps(asdict(error_response)))
                    
                except Exception as e:
                    error_response = MCPResponse(
                        id=data.get("id", "unknown"),
                        error={"code": -32603, "message": f"Internal error: {str(e)}"}
                    )
                    await websocket.send(json.dumps(asdict(error_response)))
        
        except websockets.exceptions.ConnectionClosed:
            pass
        
        finally:
            self.clients.remove(websocket)
            print(f"MCP client disconnected: {client_id}")
    
    async def process_message(self, data: Dict[str, Any]) -> MCPResponse:
        """Process incoming MCP messages"""
        message_id = data.get("id", str(uuid.uuid4()))
        method = data.get("method")
        params = data.get("params", {})
        
        if method == "initialize":
            return await self._handle_initialize(message_id, params)
        
        elif method == "tools/list":
            return await self._handle_tools_list(message_id)
        
        elif method == "tools/call":
            return await self._handle_tools_call(message_id, params)
        
        elif method == "resources/list":
            return await self._handle_resources_list(message_id)
        
        elif method == "resources/read":
            return await self._handle_resources_read(message_id, params)
        
        elif method == "notifications/subscribe":
            return await self._handle_notifications_subscribe(message_id, params)
        
        else:
            return MCPResponse(
                id=message_id,
                error={"code": -32601, "message": f"Method not found: {method}"}
            )
    
    async def _handle_initialize(self, message_id: str, params: Dict[str, Any]) -> MCPResponse:
        """Handle initialization request"""
        return MCPResponse(
            id=message_id,
            result={
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {"listChanged": True},
                    "resources": {"subscribe": True, "listChanged": True},
                    "notifications": {"subscribe": True}
                },
                "serverInfo": {
                    "name": "Augura Coder Agent",
                    "version": "1.0.0",
                    "description": "Advanced AI programming agent with MCP support"
                }
            }
        )
    
    async def _handle_tools_list(self, message_id: str) -> MCPResponse:
        """Handle tools list request"""
        tools_list = []
        for tool in self.tools.values():
            tools_list.append({
                "name": tool.name,
                "description": tool.description,
                "inputSchema": tool.input_schema
            })
        
        return MCPResponse(
            id=message_id,
            result={"tools": tools_list}
        )
    
    async def _handle_tools_call(self, message_id: str, params: Dict[str, Any]) -> MCPResponse:
        """Handle tool call request"""
        tool_name = params.get("name")
        arguments = params.get("arguments", {})
        
        if tool_name not in self.tools:
            return MCPResponse(
                id=message_id,
                error={"code": -32602, "message": f"Tool not found: {tool_name}"}
            )
        
        tool = self.tools[tool_name]
        
        try:
            if tool.handler:
                result = await tool.handler(arguments)
            else:
                result = {"error": "Tool handler not implemented"}
            
            return MCPResponse(
                id=message_id,
                result={"content": [{"type": "text", "text": json.dumps(result, indent=2)}]}
            )
            
        except Exception as e:
            return MCPResponse(
                id=message_id,
                error={"code": -32603, "message": f"Tool execution error: {str(e)}"}
            )
    
    async def _handle_resources_list(self, message_id: str) -> MCPResponse:
        """Handle resources list request"""
        resources_list = []
        for resource in self.resources.values():
            resources_list.append({
                "uri": resource.uri,
                "name": resource.name,
                "description": resource.description,
                "mimeType": resource.mime_type
            })
        
        return MCPResponse(
            id=message_id,
            result={"resources": resources_list}
        )
    
    async def _handle_resources_read(self, message_id: str, params: Dict[str, Any]) -> MCPResponse:
        """Handle resource read request"""
        uri = params.get("uri")
        
        if uri not in self.resources:
            return MCPResponse(
                id=message_id,
                error={"code": -32602, "message": f"Resource not found: {uri}"}
            )
        
        try:
            content = await self._get_resource_content(uri)
            
            return MCPResponse(
                id=message_id,
                result={
                    "contents": [{
                        "uri": uri,
                        "mimeType": self.resources[uri].mime_type,
                        "text": json.dumps(content, indent=2)
                    }]
                }
            )
            
        except Exception as e:
            return MCPResponse(
                id=message_id,
                error={"code": -32603, "message": f"Resource read error: {str(e)}"}
            )
    
    async def _handle_notifications_subscribe(self, message_id: str, params: Dict[str, Any]) -> MCPResponse:
        """Handle notification subscription request"""
        # For now, just acknowledge the subscription
        return MCPResponse(
            id=message_id,
            result={"subscribed": True}
        )
    
    # Tool handlers
    async def _handle_file_operations(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Handle file operations tool"""
        operation = args.get("operation")
        file_path = args.get("path")
        content = args.get("content", "")
        encoding = args.get("encoding", "utf-8")
        
        full_path = self.project_path / file_path
        
        try:
            if operation == "read":
                if not full_path.exists():
                    return {"error": f"File not found: {file_path}"}
                
                with open(full_path, 'r', encoding=encoding) as f:
                    return {"content": f.read(), "path": file_path}
            
            elif operation == "write":
                full_path.parent.mkdir(parents=True, exist_ok=True)
                with open(full_path, 'w', encoding=encoding) as f:
                    f.write(content)
                return {"success": True, "path": file_path, "message": "File written successfully"}
            
            elif operation == "create":
                if full_path.exists():
                    return {"error": f"File already exists: {file_path}"}
                
                full_path.parent.mkdir(parents=True, exist_ok=True)
                with open(full_path, 'w', encoding=encoding) as f:
                    f.write(content)
                return {"success": True, "path": file_path, "message": "File created successfully"}
            
            elif operation == "delete":
                if not full_path.exists():
                    return {"error": f"File not found: {file_path}"}
                
                full_path.unlink()
                return {"success": True, "path": file_path, "message": "File deleted successfully"}
            
            elif operation == "list":
                if not full_path.exists():
                    return {"error": f"Path not found: {file_path}"}
                
                if full_path.is_file():
                    return {"files": [file_path], "type": "file"}
                
                files = []
                for item in full_path.iterdir():
                    files.append({
                        "name": item.name,
                        "path": str(item.relative_to(self.project_path)),
                        "type": "directory" if item.is_dir() else "file",
                        "size": item.stat().st_size if item.is_file() else None
                    })
                
                return {"files": files, "type": "directory"}
            
            else:
                return {"error": f"Unknown operation: {operation}"}
        
        except Exception as e:
            return {"error": f"File operation failed: {str(e)}"}
    
    async def _handle_code_analysis(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Handle code analysis tool"""
        file_path = args.get("file_path")
        analysis_type = args.get("analysis_type", "structure")
        
        # This would integrate with the ProjectIndexer
        # For now, return a placeholder
        return {
            "file_path": file_path,
            "analysis_type": analysis_type,
            "result": "Code analysis not yet implemented in MCP integration"
        }
    
    async def _handle_project_search(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Handle project search tool"""
        query = args.get("query")
        search_type = args.get("search_type", "text")
        limit = args.get("limit", 10)
        
        # This would integrate with the RAG engine
        # For now, return a placeholder
        return {
            "query": query,
            "search_type": search_type,
            "results": [],
            "message": "Project search not yet implemented in MCP integration"
        }
    
    async def _handle_git_operations(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Handle Git operations tool"""
        operation = args.get("operation")
        git_args = args.get("args", [])
        
        try:
            import subprocess
            
            cmd = ["git", operation] + git_args
            result = subprocess.run(
                cmd,
                cwd=self.project_path,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            return {
                "operation": operation,
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "return_code": result.returncode
            }
        
        except subprocess.TimeoutExpired:
            return {"error": "Git operation timed out"}
        except Exception as e:
            return {"error": f"Git operation failed: {str(e)}"}
    
    async def _handle_terminal_execution(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Handle terminal execution tool"""
        command = args.get("command")
        working_dir = args.get("working_directory", str(self.project_path))
        timeout = args.get("timeout", 30)
        capture_output = args.get("capture_output", True)
        
        try:
            import subprocess
            
            result = subprocess.run(
                command,
                shell=True,
                cwd=working_dir,
                capture_output=capture_output,
                text=True,
                timeout=timeout
            )
            
            return {
                "command": command,
                "success": result.returncode == 0,
                "stdout": result.stdout if capture_output else None,
                "stderr": result.stderr if capture_output else None,
                "return_code": result.returncode
            }
        
        except subprocess.TimeoutExpired:
            return {"error": "Command timed out"}
        except Exception as e:
            return {"error": f"Command execution failed: {str(e)}"}
    
    async def _get_resource_content(self, uri: str) -> Dict[str, Any]:
        """Get content for a specific resource"""
        if uri == "project://structure":
            return await self._get_project_structure()
        
        elif uri == "project://config":
            return await self._get_project_config()
        
        elif uri == "project://metrics":
            return await self._get_project_metrics()
        
        elif uri == "project://dependencies":
            return await self._get_project_dependencies()
        
        else:
            raise ValueError(f"Unknown resource URI: {uri}")
    
    async def _get_project_structure(self) -> Dict[str, Any]:
        """Get project file structure"""
        structure = {}
        
        def build_tree(path: Path, max_depth: int = 3, current_depth: int = 0):
            if current_depth >= max_depth:
                return "..."
            
            items = {}
            try:
                for item in path.iterdir():
                    if item.name.startswith('.'):
                        continue
                    
                    if item.is_dir():
                        items[item.name] = build_tree(item, max_depth, current_depth + 1)
                    else:
                        items[item.name] = {
                            "type": "file",
                            "size": item.stat().st_size
                        }
            except PermissionError:
                return "Permission denied"
            
            return items
        
        structure = build_tree(self.project_path)
        
        return {
            "project_path": str(self.project_path),
            "structure": structure,
            "generated_at": time.time()
        }
    
    async def _get_project_config(self) -> Dict[str, Any]:
        """Get project configuration"""
        config_files = [
            "package.json", "pyproject.toml", "requirements.txt",
            "Cargo.toml", "pom.xml", "build.gradle", "CMakeLists.txt"
        ]
        
        configs = {}
        
        for config_file in config_files:
            config_path = self.project_path / config_file
            if config_path.exists():
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        configs[config_file] = f.read()
                except Exception as e:
                    configs[config_file] = f"Error reading file: {str(e)}"
        
        return {
            "project_path": str(self.project_path),
            "config_files": configs,
            "generated_at": time.time()
        }
    
    async def _get_project_metrics(self) -> Dict[str, Any]:
        """Get project code metrics"""
        # This would integrate with the ProjectIndexer
        # For now, return basic file counts
        
        file_counts = {}
        total_files = 0
        total_size = 0
        
        for file_path in self.project_path.rglob("*"):
            if file_path.is_file() and not any(part.startswith('.') for part in file_path.parts):
                ext = file_path.suffix.lower()
                file_counts[ext] = file_counts.get(ext, 0) + 1
                total_files += 1
                total_size += file_path.stat().st_size
        
        return {
            "project_path": str(self.project_path),
            "total_files": total_files,
            "total_size": total_size,
            "file_types": file_counts,
            "generated_at": time.time()
        }
    
    async def _get_project_dependencies(self) -> Dict[str, Any]:
        """Get project dependencies"""
        dependencies = {}
        
        # Check package.json
        package_json = self.project_path / "package.json"
        if package_json.exists():
            try:
                with open(package_json, 'r') as f:
                    data = json.load(f)
                    dependencies["npm"] = {
                        "dependencies": data.get("dependencies", {}),
                        "devDependencies": data.get("devDependencies", {})
                    }
            except Exception as e:
                dependencies["npm"] = {"error": str(e)}
        
        # Check requirements.txt
        requirements_txt = self.project_path / "requirements.txt"
        if requirements_txt.exists():
            try:
                with open(requirements_txt, 'r') as f:
                    deps = [line.strip() for line in f if line.strip() and not line.startswith('#')]
                    dependencies["pip"] = {"requirements": deps}
            except Exception as e:
                dependencies["pip"] = {"error": str(e)}
        
        return {
            "project_path": str(self.project_path),
            "dependencies": dependencies,
            "generated_at": time.time()
        }
    
    def stop_server(self):
        """Stop the MCP server"""
        if self.server:
            self.server.close()
            self.is_running = False
            print("MCP Server stopped")

class MCPClient:
    """MCP Client for connecting to other MCP servers"""
    
    def __init__(self, server_url: str):
        self.server_url = server_url
        self.websocket = None
        self.is_connected = False
        self.message_handlers = {}
        self.request_id = 0
    
    async def connect(self):
        """Connect to MCP server"""
        try:
            self.websocket = await websockets.connect(self.server_url)
            self.is_connected = True
            print(f"Connected to MCP server: {self.server_url}")
            
            # Initialize connection
            await self.send_message("initialize", {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {},
                    "resources": {}
                },
                "clientInfo": {
                    "name": "Augura Coder Agent Client",
                    "version": "1.0.0"
                }
            })
            
        except Exception as e:
            print(f"Failed to connect to MCP server: {e}")
            self.is_connected = False
    
    async def send_message(self, method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Send message to MCP server"""
        if not self.is_connected:
            raise Exception("Not connected to MCP server")
        
        self.request_id += 1
        message = {
            "id": str(self.request_id),
            "method": method,
            "params": params or {}
        }
        
        await self.websocket.send(json.dumps(message))
        
        # Wait for response
        response = await self.websocket.recv()
        return json.loads(response)
    
    async def list_tools(self) -> List[Dict[str, Any]]:
        """List available tools"""
        response = await self.send_message("tools/list")
        return response.get("result", {}).get("tools", [])
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool"""
        response = await self.send_message("tools/call", {
            "name": tool_name,
            "arguments": arguments
        })
        return response.get("result", {})
    
    async def list_resources(self) -> List[Dict[str, Any]]:
        """List available resources"""
        response = await self.send_message("resources/list")
        return response.get("result", {}).get("resources", [])
    
    async def read_resource(self, uri: str) -> Dict[str, Any]:
        """Read a resource"""
        response = await self.send_message("resources/read", {"uri": uri})
        return response.get("result", {})
    
    async def disconnect(self):
        """Disconnect from MCP server"""
        if self.websocket:
            await self.websocket.close()
            self.is_connected = False
            print("Disconnected from MCP server")

def start_mcp_server(project_path: str, port: int = 8765):
    """Start MCP server in a separate thread"""
    server = MCPServer(project_path, port)
    
    def run_server():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(server.start_server())
    
    thread = threading.Thread(target=run_server, daemon=True)
    thread.start()
    
    return server

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Augura MCP Server")
    parser.add_argument("project_path", help="Path to the project")
    parser.add_argument("--port", type=int, default=8765, help="Server port")
    
    args = parser.parse_args()
    
    # Start MCP server
    server = MCPServer(args.project_path, args.port)
    
    try:
        asyncio.run(server.start_server())
    except KeyboardInterrupt:
        print("\nShutting down MCP server...")
        server.stop_server()
