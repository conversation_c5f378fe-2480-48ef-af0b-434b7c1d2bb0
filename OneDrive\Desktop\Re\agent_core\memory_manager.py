#!/usr/bin/env python3
"""
Advanced Memory Management System for Augura Coder Agent
Handles persistent memory, learning, and context management
"""

import os
import sys
import json
import sqlite3
import pickle
import time
import threading
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
from collections import defaultdict, deque
import hashlib
from datetime import datetime, timedelta

@dataclass
class MemoryEntry:
    """Base class for memory entries"""
    id: str
    timestamp: float
    content: Dict[str, Any]
    memory_type: str
    importance: float = 0.5
    access_count: int = 0
    last_accessed: float = 0.0

@dataclass
class ConversationMemory(MemoryEntry):
    """Memory for conversations"""
    user_message: str = ""
    assistant_response: str = ""
    context: Dict[str, Any] = None
    feedback: Optional[str] = None
    success_rating: Optional[float] = None

    def __post_init__(self):
        super().__post_init__()
        if self.context is None:
            self.context = {}

@dataclass
class CodeMemory(MemoryEntry):
    """Memory for code-related interactions"""
    file_path: str = ""
    operation: str = ""  # 'create', 'modify', 'delete', 'analyze'
    code_before: Optional[str] = None
    code_after: Optional[str] = None
    explanation: Optional[str] = None
    success: bool = True

@dataclass
class LearningMemory(MemoryEntry):
    """Memory for learned patterns and preferences"""
    pattern_type: str = ""  # 'user_preference', 'code_style', 'error_pattern', 'success_pattern'
    pattern_data: Dict[str, Any] = None
    confidence: float = 0.5
    evidence_count: int = 1

    def __post_init__(self):
        super().__post_init__()
        if self.pattern_data is None:
            self.pattern_data = {}

@dataclass
class ProjectMemory(MemoryEntry):
    """Memory for project-specific information"""
    project_path: str = ""
    project_info: Dict[str, Any] = None
    file_structure: Dict[str, Any] = None
    dependencies: List[str] = None
    patterns: Dict[str, Any] = None

    def __post_init__(self):
        super().__post_init__()
        if self.project_info is None:
            self.project_info = {}
        if self.file_structure is None:
            self.file_structure = {}
        if self.dependencies is None:
            self.dependencies = []
        if self.patterns is None:
            self.patterns = {}

class MemoryStore:
    """Low-level memory storage interface"""

    def __init__(self, db_path: str):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self.conn = sqlite3.connect(str(self.db_path), check_same_thread=False)
        self.lock = threading.RLock()
        self._init_database()

    def _init_database(self):
        """Initialize the memory database"""
        with self.lock:
            self.conn.executescript('''
                CREATE TABLE IF NOT EXISTS memory_entries (
                    id TEXT PRIMARY KEY,
                    timestamp REAL NOT NULL,
                    memory_type TEXT NOT NULL,
                    importance REAL DEFAULT 0.5,
                    access_count INTEGER DEFAULT 0,
                    last_accessed REAL DEFAULT 0.0,
                    content_json TEXT NOT NULL,
                    content_blob BLOB
                );

                CREATE TABLE IF NOT EXISTS conversation_memory (
                    id TEXT PRIMARY KEY,
                    user_message TEXT NOT NULL,
                    assistant_response TEXT NOT NULL,
                    context_json TEXT,
                    feedback TEXT,
                    success_rating REAL,
                    FOREIGN KEY (id) REFERENCES memory_entries (id) ON DELETE CASCADE
                );

                CREATE TABLE IF NOT EXISTS code_memory (
                    id TEXT PRIMARY KEY,
                    file_path TEXT NOT NULL,
                    operation TEXT NOT NULL,
                    code_before TEXT,
                    code_after TEXT,
                    explanation TEXT,
                    success BOOLEAN DEFAULT TRUE,
                    FOREIGN KEY (id) REFERENCES memory_entries (id) ON DELETE CASCADE
                );

                CREATE TABLE IF NOT EXISTS learning_memory (
                    id TEXT PRIMARY KEY,
                    pattern_type TEXT NOT NULL,
                    pattern_data_json TEXT NOT NULL,
                    confidence REAL DEFAULT 0.5,
                    evidence_count INTEGER DEFAULT 1,
                    FOREIGN KEY (id) REFERENCES memory_entries (id) ON DELETE CASCADE
                );

                CREATE TABLE IF NOT EXISTS project_memory (
                    id TEXT PRIMARY KEY,
                    project_path TEXT NOT NULL,
                    project_info_json TEXT NOT NULL,
                    file_structure_json TEXT,
                    dependencies_json TEXT,
                    patterns_json TEXT,
                    FOREIGN KEY (id) REFERENCES memory_entries (id) ON DELETE CASCADE
                );

                CREATE INDEX IF NOT EXISTS idx_memory_type ON memory_entries (memory_type);
                CREATE INDEX IF NOT EXISTS idx_memory_timestamp ON memory_entries (timestamp);
                CREATE INDEX IF NOT EXISTS idx_memory_importance ON memory_entries (importance);
                CREATE INDEX IF NOT EXISTS idx_code_file_path ON code_memory (file_path);
                CREATE INDEX IF NOT EXISTS idx_learning_pattern_type ON learning_memory (pattern_type);
                CREATE INDEX IF NOT EXISTS idx_project_path ON project_memory (project_path);
            ''')
            self.conn.commit()

    def store_memory(self, memory: MemoryEntry) -> bool:
        """Store a memory entry"""
        try:
            with self.lock:
                # Store base memory entry
                self.conn.execute('''
                    INSERT OR REPLACE INTO memory_entries
                    (id, timestamp, memory_type, importance, access_count, last_accessed, content_json)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    memory.id, memory.timestamp, memory.memory_type,
                    memory.importance, memory.access_count, memory.last_accessed,
                    json.dumps(memory.content)
                ))

                # Store type-specific data
                if isinstance(memory, ConversationMemory):
                    self.conn.execute('''
                        INSERT OR REPLACE INTO conversation_memory
                        (id, user_message, assistant_response, context_json, feedback, success_rating)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (
                        memory.id, memory.user_message, memory.assistant_response,
                        json.dumps(memory.context), memory.feedback, memory.success_rating
                    ))

                elif isinstance(memory, CodeMemory):
                    self.conn.execute('''
                        INSERT OR REPLACE INTO code_memory
                        (id, file_path, operation, code_before, code_after, explanation, success)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        memory.id, memory.file_path, memory.operation,
                        memory.code_before, memory.code_after, memory.explanation, memory.success
                    ))

                elif isinstance(memory, LearningMemory):
                    self.conn.execute('''
                        INSERT OR REPLACE INTO learning_memory
                        (id, pattern_type, pattern_data_json, confidence, evidence_count)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (
                        memory.id, memory.pattern_type, json.dumps(memory.pattern_data),
                        memory.confidence, memory.evidence_count
                    ))

                elif isinstance(memory, ProjectMemory):
                    self.conn.execute('''
                        INSERT OR REPLACE INTO project_memory
                        (id, project_path, project_info_json, file_structure_json, dependencies_json, patterns_json)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (
                        memory.id, memory.project_path, json.dumps(memory.project_info),
                        json.dumps(memory.file_structure), json.dumps(memory.dependencies),
                        json.dumps(memory.patterns)
                    ))

                self.conn.commit()
                return True

        except Exception as e:
            print(f"Error storing memory: {e}")
            return False

    def retrieve_memory(self, memory_id: str) -> Optional[MemoryEntry]:
        """Retrieve a specific memory entry"""
        try:
            with self.lock:
                cursor = self.conn.cursor()
                cursor.execute('''
                    SELECT id, timestamp, memory_type, importance, access_count, last_accessed, content_json
                    FROM memory_entries WHERE id = ?
                ''', (memory_id,))

                row = cursor.fetchone()
                if not row:
                    return None

                id, timestamp, memory_type, importance, access_count, last_accessed, content_json = row
                content = json.loads(content_json)

                # Update access count
                self.conn.execute('''
                    UPDATE memory_entries
                    SET access_count = access_count + 1, last_accessed = ?
                    WHERE id = ?
                ''', (time.time(), memory_id))

                # Retrieve type-specific data
                if memory_type == 'conversation':
                    cursor.execute('''
                        SELECT user_message, assistant_response, context_json, feedback, success_rating
                        FROM conversation_memory WHERE id = ?
                    ''', (memory_id,))
                    conv_row = cursor.fetchone()
                    if conv_row:
                        user_message, assistant_response, context_json, feedback, success_rating = conv_row
                        context = json.loads(context_json) if context_json else {}

                        return ConversationMemory(
                            id=id, timestamp=timestamp, content=content,
                            memory_type=memory_type, importance=importance,
                            access_count=access_count + 1, last_accessed=time.time(),
                            user_message=user_message, assistant_response=assistant_response,
                            context=context, feedback=feedback, success_rating=success_rating
                        )

                elif memory_type == 'code':
                    cursor.execute('''
                        SELECT file_path, operation, code_before, code_after, explanation, success
                        FROM code_memory WHERE id = ?
                    ''', (memory_id,))
                    code_row = cursor.fetchone()
                    if code_row:
                        file_path, operation, code_before, code_after, explanation, success = code_row

                        return CodeMemory(
                            id=id, timestamp=timestamp, content=content,
                            memory_type=memory_type, importance=importance,
                            access_count=access_count + 1, last_accessed=time.time(),
                            file_path=file_path, operation=operation,
                            code_before=code_before, code_after=code_after,
                            explanation=explanation, success=bool(success)
                        )

                elif memory_type == 'learning':
                    cursor.execute('''
                        SELECT pattern_type, pattern_data_json, confidence, evidence_count
                        FROM learning_memory WHERE id = ?
                    ''', (memory_id,))
                    learn_row = cursor.fetchone()
                    if learn_row:
                        pattern_type, pattern_data_json, confidence, evidence_count = learn_row
                        pattern_data = json.loads(pattern_data_json)

                        return LearningMemory(
                            id=id, timestamp=timestamp, content=content,
                            memory_type=memory_type, importance=importance,
                            access_count=access_count + 1, last_accessed=time.time(),
                            pattern_type=pattern_type, pattern_data=pattern_data,
                            confidence=confidence, evidence_count=evidence_count
                        )

                elif memory_type == 'project':
                    cursor.execute('''
                        SELECT project_path, project_info_json, file_structure_json, dependencies_json, patterns_json
                        FROM project_memory WHERE id = ?
                    ''', (memory_id,))
                    proj_row = cursor.fetchone()
                    if proj_row:
                        project_path, project_info_json, file_structure_json, dependencies_json, patterns_json = proj_row

                        return ProjectMemory(
                            id=id, timestamp=timestamp, content=content,
                            memory_type=memory_type, importance=importance,
                            access_count=access_count + 1, last_accessed=time.time(),
                            project_path=project_path,
                            project_info=json.loads(project_info_json),
                            file_structure=json.loads(file_structure_json) if file_structure_json else {},
                            dependencies=json.loads(dependencies_json) if dependencies_json else [],
                            patterns=json.loads(patterns_json) if patterns_json else {}
                        )

                self.conn.commit()
                return None

        except Exception as e:
            print(f"Error retrieving memory: {e}")
            return None

    def search_memories(self, memory_type: str = None, limit: int = 100,
                       order_by: str = 'timestamp') -> List[MemoryEntry]:
        """Search for memories with filters"""
        try:
            with self.lock:
                cursor = self.conn.cursor()

                query = 'SELECT id FROM memory_entries'
                params = []

                if memory_type:
                    query += ' WHERE memory_type = ?'
                    params.append(memory_type)

                query += f' ORDER BY {order_by} DESC LIMIT ?'
                params.append(limit)

                cursor.execute(query, params)

                memories = []
                for (memory_id,) in cursor.fetchall():
                    memory = self.retrieve_memory(memory_id)
                    if memory:
                        memories.append(memory)

                return memories

        except Exception as e:
            print(f"Error searching memories: {e}")
            return []

    def delete_memory(self, memory_id: str) -> bool:
        """Delete a memory entry"""
        try:
            with self.lock:
                self.conn.execute('DELETE FROM memory_entries WHERE id = ?', (memory_id,))
                self.conn.commit()
                return True
        except Exception as e:
            print(f"Error deleting memory: {e}")
            return False

    def cleanup_old_memories(self, days_to_keep: int = 30) -> int:
        """Clean up old memories"""
        try:
            with self.lock:
                cutoff_time = time.time() - (days_to_keep * 24 * 60 * 60)

                cursor = self.conn.cursor()
                cursor.execute('''
                    DELETE FROM memory_entries
                    WHERE timestamp < ? AND importance < 0.7
                ''', (cutoff_time,))

                deleted_count = cursor.rowcount
                self.conn.commit()

                return deleted_count

        except Exception as e:
            print(f"Error cleaning up memories: {e}")
            return 0

class MemoryManager:
    """High-level memory management interface"""

    def __init__(self, memory_dir: str):
        self.memory_dir = Path(memory_dir)
        self.memory_dir.mkdir(parents=True, exist_ok=True)

        # Initialize storage
        self.store = MemoryStore(self.memory_dir / 'memories.db')

        # In-memory caches
        self.recent_conversations = deque(maxlen=50)
        self.active_patterns = {}
        self.project_contexts = {}

        # Learning system
        self.pattern_detector = PatternDetector()
        self.preference_learner = PreferenceLearner()

        # Background cleanup thread
        self.cleanup_thread = threading.Thread(target=self._background_cleanup, daemon=True)
        self.cleanup_thread.start()

    def store_conversation(self, user_message: str, assistant_response: str,
                          context: Dict[str, Any] = None, feedback: str = None,
                          success_rating: float = None) -> str:
        """Store a conversation in memory"""
        memory_id = self._generate_memory_id('conv')

        memory = ConversationMemory(
            id=memory_id,
            timestamp=time.time(),
            content={'summary': user_message[:100] + '...'},
            memory_type='conversation',
            importance=self._calculate_conversation_importance(user_message, assistant_response),
            user_message=user_message,
            assistant_response=assistant_response,
            context=context or {},
            feedback=feedback,
            success_rating=success_rating
        )

        if self.store.store_memory(memory):
            self.recent_conversations.append(memory)

            # Learn from this conversation
            self._learn_from_conversation(memory)

            return memory_id

        return None

    def store_code_interaction(self, file_path: str, operation: str,
                             code_before: str = None, code_after: str = None,
                             explanation: str = None, success: bool = True) -> str:
        """Store a code interaction in memory"""
        memory_id = self._generate_memory_id('code')

        memory = CodeMemory(
            id=memory_id,
            timestamp=time.time(),
            content={'file': file_path, 'op': operation},
            memory_type='code',
            importance=self._calculate_code_importance(operation, success),
            file_path=file_path,
            operation=operation,
            code_before=code_before,
            code_after=code_after,
            explanation=explanation,
            success=success
        )

        if self.store.store_memory(memory):
            # Learn from this code interaction
            self._learn_from_code_interaction(memory)

            return memory_id

        return None

    def store_learning_pattern(self, pattern_type: str, pattern_data: Dict[str, Any],
                             confidence: float = 0.5, evidence_count: int = 1) -> str:
        """Store a learned pattern"""
        memory_id = self._generate_memory_id('learn')

        memory = LearningMemory(
            id=memory_id,
            timestamp=time.time(),
            content={'pattern': pattern_type},
            memory_type='learning',
            importance=confidence,
            pattern_type=pattern_type,
            pattern_data=pattern_data,
            confidence=confidence,
            evidence_count=evidence_count
        )

        if self.store.store_memory(memory):
            self.active_patterns[pattern_type] = memory
            return memory_id

        return None

    def store_project_context(self, project_path: str, project_info: Dict[str, Any],
                            file_structure: Dict[str, Any] = None,
                            dependencies: List[str] = None,
                            patterns: Dict[str, Any] = None) -> str:
        """Store project context"""
        memory_id = self._generate_memory_id('proj')

        memory = ProjectMemory(
            id=memory_id,
            timestamp=time.time(),
            content={'project': project_path},
            memory_type='project',
            importance=0.8,  # Projects are generally important
            project_path=project_path,
            project_info=project_info,
            file_structure=file_structure or {},
            dependencies=dependencies or [],
            patterns=patterns or {}
        )

        if self.store.store_memory(memory):
            self.project_contexts[project_path] = memory
            return memory_id

        return None

    def get_relevant_conversations(self, query: str, limit: int = 5) -> List[ConversationMemory]:
        """Get conversations relevant to a query"""
        all_conversations = self.store.search_memories('conversation', limit=50)

        # Simple relevance scoring
        relevant = []
        query_lower = query.lower()

        for conv in all_conversations:
            if isinstance(conv, ConversationMemory):
                score = 0.0

                # Check user message
                if query_lower in conv.user_message.lower():
                    score += 1.0

                # Check assistant response
                if query_lower in conv.assistant_response.lower():
                    score += 0.8

                # Check context
                context_str = json.dumps(conv.context).lower()
                if query_lower in context_str:
                    score += 0.6

                # Boost recent conversations
                age_hours = (time.time() - conv.timestamp) / 3600
                if age_hours < 24:
                    score += 0.5
                elif age_hours < 168:  # 1 week
                    score += 0.2

                if score > 0:
                    relevant.append((conv, score))

        # Sort by relevance and return top results
        relevant.sort(key=lambda x: x[1], reverse=True)
        return [conv for conv, score in relevant[:limit]]

    def get_code_history(self, file_path: str = None, operation: str = None,
                        limit: int = 10) -> List[CodeMemory]:
        """Get code interaction history"""
        all_code_memories = self.store.search_memories('code', limit=100)

        filtered = []
        for memory in all_code_memories:
            if isinstance(memory, CodeMemory):
                if file_path and file_path not in memory.file_path:
                    continue
                if operation and operation != memory.operation:
                    continue
                filtered.append(memory)

        return filtered[:limit]

    def get_learned_patterns(self, pattern_type: str = None) -> List[LearningMemory]:
        """Get learned patterns"""
        all_patterns = self.store.search_memories('learning', limit=100)

        if pattern_type:
            return [p for p in all_patterns
                   if isinstance(p, LearningMemory) and p.pattern_type == pattern_type]

        return [p for p in all_patterns if isinstance(p, LearningMemory)]

    def get_project_context(self, project_path: str) -> Optional[ProjectMemory]:
        """Get project context"""
        if project_path in self.project_contexts:
            return self.project_contexts[project_path]

        # Search in database
        all_projects = self.store.search_memories('project', limit=50)
        for proj in all_projects:
            if isinstance(proj, ProjectMemory) and proj.project_path == project_path:
                self.project_contexts[project_path] = proj
                return proj

        return None

    def update_pattern_confidence(self, pattern_type: str, evidence: Dict[str, Any],
                                positive: bool = True):
        """Update confidence in a learned pattern"""
        patterns = self.get_learned_patterns(pattern_type)

        for pattern in patterns:
            if positive:
                pattern.confidence = min(1.0, pattern.confidence + 0.1)
                pattern.evidence_count += 1
            else:
                pattern.confidence = max(0.0, pattern.confidence - 0.05)

            # Update in storage
            self.store.store_memory(pattern)

    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory statistics"""
        stats = {
            'total_memories': 0,
            'by_type': defaultdict(int),
            'recent_conversations': len(self.recent_conversations),
            'active_patterns': len(self.active_patterns),
            'project_contexts': len(self.project_contexts)
        }

        # Count memories by type
        for memory_type in ['conversation', 'code', 'learning', 'project']:
            memories = self.store.search_memories(memory_type, limit=1000)
            count = len(memories)
            stats['by_type'][memory_type] = count
            stats['total_memories'] += count

        return dict(stats)

    def _generate_memory_id(self, prefix: str) -> str:
        """Generate a unique memory ID"""
        timestamp = str(time.time())
        random_part = hashlib.md5(f"{prefix}_{timestamp}".encode()).hexdigest()[:8]
        return f"{prefix}_{int(time.time())}_{random_part}"

    def _calculate_conversation_importance(self, user_message: str, assistant_response: str) -> float:
        """Calculate importance score for a conversation"""
        importance = 0.5  # Base importance

        # Longer conversations are more important
        total_length = len(user_message) + len(assistant_response)
        if total_length > 1000:
            importance += 0.2
        elif total_length > 500:
            importance += 0.1

        # Code-related conversations are more important
        code_keywords = ['function', 'class', 'variable', 'import', 'def', 'return']
        if any(keyword in user_message.lower() for keyword in code_keywords):
            importance += 0.2

        # Questions are important
        if '?' in user_message:
            importance += 0.1

        return min(1.0, importance)

    def _calculate_code_importance(self, operation: str, success: bool) -> float:
        """Calculate importance score for code interaction"""
        importance = 0.6  # Base importance for code

        # Different operations have different importance
        operation_weights = {
            'create': 0.8,
            'modify': 0.7,
            'delete': 0.6,
            'analyze': 0.4,
            'refactor': 0.9
        }

        importance += operation_weights.get(operation, 0.0)

        # Successful operations are more important
        if success:
            importance += 0.1
        else:
            importance -= 0.1

        return min(1.0, max(0.0, importance))

    def _learn_from_conversation(self, conversation: ConversationMemory):
        """Learn patterns from conversation"""
        # Detect user preferences
        preferences = self.preference_learner.detect_preferences(
            conversation.user_message, conversation.assistant_response
        )

        for pref_type, pref_data in preferences.items():
            self.store_learning_pattern(
                f"user_preference_{pref_type}",
                pref_data,
                confidence=0.6
            )

        # Detect conversation patterns
        patterns = self.pattern_detector.detect_conversation_patterns(conversation)

        for pattern_type, pattern_data in patterns.items():
            self.store_learning_pattern(
                f"conversation_{pattern_type}",
                pattern_data,
                confidence=0.5
            )

    def _learn_from_code_interaction(self, code_memory: CodeMemory):
        """Learn patterns from code interaction"""
        # Detect code style patterns
        if code_memory.code_before and code_memory.code_after:
            style_patterns = self.pattern_detector.detect_code_style_patterns(
                code_memory.code_before, code_memory.code_after
            )

            for pattern_type, pattern_data in style_patterns.items():
                self.store_learning_pattern(
                    f"code_style_{pattern_type}",
                    pattern_data,
                    confidence=0.7 if code_memory.success else 0.3
                )

        # Detect operation patterns
        operation_patterns = self.pattern_detector.detect_operation_patterns(code_memory)

        for pattern_type, pattern_data in operation_patterns.items():
            self.store_learning_pattern(
                f"operation_{pattern_type}",
                pattern_data,
                confidence=0.6
            )

    def _background_cleanup(self):
        """Background thread for memory cleanup"""
        while True:
            try:
                # Sleep for 1 hour
                time.sleep(3600)

                # Cleanup old memories
                deleted_count = self.store.cleanup_old_memories(days_to_keep=30)
                if deleted_count > 0:
                    print(f"Cleaned up {deleted_count} old memories")

            except Exception as e:
                print(f"Error in background cleanup: {e}")

class PatternDetector:
    """Detects patterns in conversations and code"""

    def detect_conversation_patterns(self, conversation: ConversationMemory) -> Dict[str, Dict[str, Any]]:
        """Detect patterns in conversation"""
        patterns = {}

        # Question type patterns
        if '?' in conversation.user_message:
            question_type = self._classify_question(conversation.user_message)
            patterns['question_type'] = {
                'type': question_type,
                'message': conversation.user_message,
                'response_length': len(conversation.assistant_response)
            }

        # Response effectiveness patterns
        if conversation.success_rating:
            patterns['response_effectiveness'] = {
                'rating': conversation.success_rating,
                'response_length': len(conversation.assistant_response),
                'context_size': len(conversation.context)
            }

        return patterns

    def detect_code_style_patterns(self, code_before: str, code_after: str) -> Dict[str, Dict[str, Any]]:
        """Detect code style patterns"""
        patterns = {}

        # Indentation patterns
        before_indent = self._detect_indentation(code_before)
        after_indent = self._detect_indentation(code_after)

        if before_indent != after_indent:
            patterns['indentation_change'] = {
                'from': before_indent,
                'to': after_indent
            }

        # Naming patterns
        before_names = self._extract_names(code_before)
        after_names = self._extract_names(code_after)

        naming_changes = []
        for old_name, new_name in zip(before_names, after_names):
            if old_name != new_name:
                naming_changes.append({'from': old_name, 'to': new_name})

        if naming_changes:
            patterns['naming_changes'] = naming_changes

        return patterns

    def detect_operation_patterns(self, code_memory: CodeMemory) -> Dict[str, Dict[str, Any]]:
        """Detect operation patterns"""
        patterns = {}

        # File type patterns
        file_ext = Path(code_memory.file_path).suffix
        patterns['file_type_operation'] = {
            'file_extension': file_ext,
            'operation': code_memory.operation,
            'success': code_memory.success
        }

        # Time patterns
        hour = datetime.fromtimestamp(code_memory.timestamp).hour
        patterns['time_pattern'] = {
            'hour': hour,
            'operation': code_memory.operation
        }

        return patterns

    def _classify_question(self, message: str) -> str:
        """Classify the type of question"""
        message_lower = message.lower()

        if any(word in message_lower for word in ['how', 'how to']):
            return 'how_to'
        elif any(word in message_lower for word in ['what', 'what is']):
            return 'what_is'
        elif any(word in message_lower for word in ['why', 'why does']):
            return 'why'
        elif any(word in message_lower for word in ['where', 'where is']):
            return 'where'
        elif any(word in message_lower for word in ['when', 'when to']):
            return 'when'
        else:
            return 'other'

    def _detect_indentation(self, code: str) -> str:
        """Detect indentation style in code"""
        lines = code.split('\n')

        for line in lines:
            if line.startswith('    '):
                return 'spaces_4'
            elif line.startswith('  '):
                return 'spaces_2'
            elif line.startswith('\t'):
                return 'tabs'

        return 'none'

    def _extract_names(self, code: str) -> List[str]:
        """Extract variable/function names from code"""
        # Simple regex-based extraction
        import re

        # Function names
        func_names = re.findall(r'def\s+(\w+)', code)

        # Variable names
        var_names = re.findall(r'(\w+)\s*=', code)

        # Class names
        class_names = re.findall(r'class\s+(\w+)', code)

        return func_names + var_names + class_names

class PreferenceLearner:
    """Learns user preferences from interactions"""

    def detect_preferences(self, user_message: str, assistant_response: str) -> Dict[str, Dict[str, Any]]:
        """Detect user preferences from conversation"""
        preferences = {}

        # Code style preferences
        if 'prefer' in user_message.lower():
            style_prefs = self._extract_style_preferences(user_message)
            if style_prefs:
                preferences['code_style'] = style_prefs

        # Communication preferences
        comm_prefs = self._extract_communication_preferences(user_message, assistant_response)
        if comm_prefs:
            preferences['communication'] = comm_prefs

        # Tool preferences
        tool_prefs = self._extract_tool_preferences(user_message)
        if tool_prefs:
            preferences['tools'] = tool_prefs

        return preferences

    def _extract_style_preferences(self, message: str) -> Dict[str, Any]:
        """Extract code style preferences"""
        preferences = {}
        message_lower = message.lower()

        # Indentation preferences
        if 'spaces' in message_lower:
            preferences['indentation'] = 'spaces'
        elif 'tabs' in message_lower:
            preferences['indentation'] = 'tabs'

        # Naming preferences
        if 'camelcase' in message_lower or 'camel case' in message_lower:
            preferences['naming'] = 'camelCase'
        elif 'snake_case' in message_lower or 'snake case' in message_lower:
            preferences['naming'] = 'snake_case'

        # Quote preferences
        if 'single quotes' in message_lower:
            preferences['quotes'] = 'single'
        elif 'double quotes' in message_lower:
            preferences['quotes'] = 'double'

        return preferences

    def _extract_communication_preferences(self, user_message: str, assistant_response: str) -> Dict[str, Any]:
        """Extract communication style preferences"""
        preferences = {}

        # Response length preference
        if len(assistant_response) > 1000 and 'too long' in user_message.lower():
            preferences['response_length'] = 'short'
        elif len(assistant_response) < 200 and 'more detail' in user_message.lower():
            preferences['response_length'] = 'detailed'

        # Explanation style
        if 'explain' in user_message.lower():
            if 'simple' in user_message.lower():
                preferences['explanation_style'] = 'simple'
            elif 'detailed' in user_message.lower():
                preferences['explanation_style'] = 'detailed'

        return preferences

    def _extract_tool_preferences(self, message: str) -> Dict[str, Any]:
        """Extract tool and framework preferences"""
        preferences = {}
        message_lower = message.lower()

        # Framework preferences
        frameworks = ['react', 'vue', 'angular', 'django', 'flask', 'express', 'fastapi']
        for framework in frameworks:
            if framework in message_lower:
                preferences['preferred_framework'] = framework
                break

        # Language preferences
        languages = ['python', 'javascript', 'typescript', 'java', 'cpp', 'c++']
        for language in languages:
            if language in message_lower:
                preferences['preferred_language'] = language
                break

        return preferences

def main():
    """Main entry point for testing"""
    import argparse

    parser = argparse.ArgumentParser(description='Memory Manager Test')
    parser.add_argument('memory_dir', help='Memory directory path')
    parser.add_argument('--test', action='store_true', help='Run tests')
    parser.add_argument('--stats', action='store_true', help='Show statistics')

    args = parser.parse_args()

    # Initialize memory manager
    memory_manager = MemoryManager(args.memory_dir)

    if args.test:
        # Run some tests
        print("Testing memory manager...")

        # Test conversation storage
        conv_id = memory_manager.store_conversation(
            "How do I create a function in Python?",
            "To create a function in Python, use the 'def' keyword...",
            context={'language': 'python'},
            success_rating=0.9
        )
        print(f"Stored conversation: {conv_id}")

        # Test code interaction storage
        code_id = memory_manager.store_code_interaction(
            "test.py",
            "create",
            code_after="def hello():\n    print('Hello, world!')",
            explanation="Created a simple hello function",
            success=True
        )
        print(f"Stored code interaction: {code_id}")

        # Test pattern storage
        pattern_id = memory_manager.store_learning_pattern(
            "user_preference_indentation",
            {"style": "spaces", "count": 4},
            confidence=0.8
        )
        print(f"Stored learning pattern: {pattern_id}")

    if args.stats:
        # Show statistics
        stats = memory_manager.get_memory_stats()
        print("Memory Statistics:")
        for key, value in stats.items():
            print(f"  {key}: {value}")

if __name__ == '__main__':
    main()
