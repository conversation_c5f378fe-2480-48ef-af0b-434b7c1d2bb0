#!/usr/bin/env python3
"""
Advanced RAG (Retrieval-Augmented Generation) Engine for Augura Coder Agent
Provides intelligent context retrieval and code understanding
"""

import os
import sys
import json
import sqlite3
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import re
import hashlib
from collections import defaultdict, Counter
import math

# Try to import optional dependencies
try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    from sklearn.decomposition import TruncatedSVD
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("Warning: scikit-learn not available. Advanced RAG features disabled.")

try:
    import sentence_transformers
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    print("Warning: sentence-transformers not available. Semantic embeddings disabled.")

@dataclass
class RetrievalResult:
    """Result from RAG retrieval"""
    content: str
    source: str
    relevance_score: float
    context_type: str  # 'code', 'documentation', 'comment', 'structure'
    metadata: Dict[str, Any]
    line_numbers: Optional[Tuple[int, int]] = None

@dataclass
class CodeContext:
    """Context information for code understanding"""
    file_path: str
    function_name: Optional[str]
    class_name: Optional[str]
    imports: List[str]
    dependencies: List[str]
    related_files: List[str]
    complexity_score: float
    language: str

class SemanticEmbedder:
    """Handles semantic embeddings for code and text"""
    
    def __init__(self, model_name: str = 'all-MiniLM-L6-v2'):
        self.model = None
        self.model_name = model_name
        
        if SENTENCE_TRANSFORMERS_AVAILABLE:
            try:
                self.model = SentenceTransformer(model_name)
                print(f"Loaded semantic model: {model_name}")
            except Exception as e:
                print(f"Failed to load semantic model: {e}")
                self.model = None
    
    def encode(self, texts: List[str]) -> Optional[np.ndarray]:
        """Encode texts into semantic embeddings"""
        if self.model is None:
            return None
        
        try:
            embeddings = self.model.encode(texts, convert_to_numpy=True)
            return embeddings
        except Exception as e:
            print(f"Error encoding texts: {e}")
            return None
    
    def encode_single(self, text: str) -> Optional[np.ndarray]:
        """Encode a single text into semantic embedding"""
        result = self.encode([text])
        return result[0] if result is not None else None

class CodeChunker:
    """Intelligent code chunking for better context retrieval"""
    
    def __init__(self, max_chunk_size: int = 1000, overlap_size: int = 100):
        self.max_chunk_size = max_chunk_size
        self.overlap_size = overlap_size
    
    def chunk_code(self, content: str, language: str, file_path: str) -> List[Dict[str, Any]]:
        """Chunk code intelligently based on structure"""
        chunks = []
        
        if language == 'python':
            chunks = self._chunk_python(content, file_path)
        elif language in ['javascript', 'typescript']:
            chunks = self._chunk_javascript(content, file_path)
        else:
            chunks = self._chunk_generic(content, file_path)
        
        return chunks
    
    def _chunk_python(self, content: str, file_path: str) -> List[Dict[str, Any]]:
        """Chunk Python code by functions and classes"""
        chunks = []
        lines = content.split('\n')
        current_chunk = []
        current_start = 1
        current_context = None
        indent_level = 0
        
        for i, line in enumerate(lines):
            line_num = i + 1
            stripped = line.strip()
            current_indent = len(line) - len(line.lstrip())
            
            # Detect function or class definitions
            if stripped.startswith('def ') or stripped.startswith('class '):
                # Save previous chunk if it exists
                if current_chunk:
                    chunks.append(self._create_chunk(
                        current_chunk, current_start, line_num - 1, 
                        file_path, current_context
                    ))
                
                # Start new chunk
                current_chunk = [line]
                current_start = line_num
                current_context = stripped.split('(')[0] if '(' in stripped else stripped.split(':')[0]
                indent_level = current_indent
            
            elif current_chunk:
                # Continue current chunk
                current_chunk.append(line)
                
                # Check if we've reached the end of the function/class
                if stripped and current_indent <= indent_level and not line.startswith(' '):
                    if not stripped.startswith('#') and not stripped.startswith('"""'):
                        # End current chunk
                        chunks.append(self._create_chunk(
                            current_chunk[:-1], current_start, line_num - 1,
                            file_path, current_context
                        ))
                        
                        # Start new chunk with current line
                        current_chunk = [line]
                        current_start = line_num
                        current_context = None
                        indent_level = 0
            
            else:
                # Start new chunk for standalone code
                current_chunk = [line]
                current_start = line_num
                current_context = None
        
        # Add final chunk
        if current_chunk:
            chunks.append(self._create_chunk(
                current_chunk, current_start, len(lines),
                file_path, current_context
            ))
        
        return chunks
    
    def _chunk_javascript(self, content: str, file_path: str) -> List[Dict[str, Any]]:
        """Chunk JavaScript code by functions and classes"""
        chunks = []
        lines = content.split('\n')
        current_chunk = []
        current_start = 1
        current_context = None
        brace_level = 0
        
        for i, line in enumerate(lines):
            line_num = i + 1
            stripped = line.strip()
            
            # Count braces
            brace_level += line.count('{') - line.count('}')
            
            # Detect function or class definitions
            if (re.match(r'(?:function\s+\w+|class\s+\w+|\w+\s*[:=]\s*(?:function|\([^)]*\)\s*=>))', stripped) or
                stripped.startswith('export ') and ('function' in stripped or 'class' in stripped)):
                
                # Save previous chunk if it exists
                if current_chunk:
                    chunks.append(self._create_chunk(
                        current_chunk, current_start, line_num - 1,
                        file_path, current_context
                    ))
                
                # Start new chunk
                current_chunk = [line]
                current_start = line_num
                current_context = self._extract_js_function_name(stripped)
                brace_level = line.count('{') - line.count('}')
            
            elif current_chunk:
                current_chunk.append(line)
                
                # Check if we've reached the end of the function/class
                if brace_level == 0 and stripped.endswith('}'):
                    chunks.append(self._create_chunk(
                        current_chunk, current_start, line_num,
                        file_path, current_context
                    ))
                    
                    current_chunk = []
                    current_context = None
            
            else:
                # Start new chunk for standalone code
                current_chunk = [line]
                current_start = line_num
                current_context = None
        
        # Add final chunk
        if current_chunk:
            chunks.append(self._create_chunk(
                current_chunk, current_start, len(lines),
                file_path, current_context
            ))
        
        return chunks
    
    def _chunk_generic(self, content: str, file_path: str) -> List[Dict[str, Any]]:
        """Generic chunking for unsupported languages"""
        chunks = []
        lines = content.split('\n')
        
        # Simple line-based chunking
        for i in range(0, len(lines), self.max_chunk_size):
            chunk_lines = lines[i:i + self.max_chunk_size]
            chunks.append(self._create_chunk(
                chunk_lines, i + 1, min(i + self.max_chunk_size, len(lines)),
                file_path, None
            ))
        
        return chunks
    
    def _create_chunk(self, lines: List[str], start_line: int, end_line: int, 
                     file_path: str, context: Optional[str]) -> Dict[str, Any]:
        """Create a chunk dictionary"""
        content = '\n'.join(lines)
        
        return {
            'content': content,
            'file_path': file_path,
            'start_line': start_line,
            'end_line': end_line,
            'context': context,
            'size': len(content),
            'line_count': len(lines),
            'hash': hashlib.md5(content.encode()).hexdigest()
        }
    
    def _extract_js_function_name(self, line: str) -> Optional[str]:
        """Extract function name from JavaScript line"""
        # Function declaration
        func_match = re.match(r'function\s+(\w+)', line)
        if func_match:
            return func_match.group(1)
        
        # Arrow function or function expression
        arrow_match = re.match(r'(?:const|let|var)\s+(\w+)\s*=', line)
        if arrow_match:
            return arrow_match.group(1)
        
        # Class declaration
        class_match = re.match(r'class\s+(\w+)', line)
        if class_match:
            return class_match.group(1)
        
        return None

class ContextRetriever:
    """Retrieves relevant context for queries"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.conn = sqlite3.connect(db_path, check_same_thread=False)
        self.embedder = SemanticEmbedder()
        self.chunker = CodeChunker()
        
        # Initialize TF-IDF vectorizer
        if SKLEARN_AVAILABLE:
            self.tfidf_vectorizer = TfidfVectorizer(
                max_features=10000,
                stop_words='english',
                ngram_range=(1, 3),
                lowercase=True
            )
            self.tfidf_matrix = None
            self.document_chunks = []
            self._build_tfidf_index()
        
        # Code-specific patterns
        self.code_patterns = {
            'function_call': re.compile(r'(\w+)\s*\('),
            'class_usage': re.compile(r'(\w+)\.(\w+)'),
            'import_statement': re.compile(r'(?:import|from)\s+(\w+)'),
            'variable_assignment': re.compile(r'(\w+)\s*='),
        }
    
    def _build_tfidf_index(self):
        """Build TF-IDF index from database content"""
        if not SKLEARN_AVAILABLE:
            return
        
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT sc.content, sc.file_id, sc.start_line, sc.end_line, f.path, f.language
            FROM semantic_chunks sc
            JOIN files f ON sc.file_id = f.id
        ''')
        
        documents = []
        chunks = []
        
        for row in cursor.fetchall():
            content, file_id, start_line, end_line, file_path, language = row
            documents.append(content)
            chunks.append({
                'content': content,
                'file_id': file_id,
                'file_path': file_path,
                'start_line': start_line,
                'end_line': end_line,
                'language': language
            })
        
        if documents:
            try:
                self.tfidf_matrix = self.tfidf_vectorizer.fit_transform(documents)
                self.document_chunks = chunks
                print(f"Built TF-IDF index with {len(documents)} chunks")
            except Exception as e:
                print(f"Error building TF-IDF index: {e}")
    
    def retrieve_context(self, query: str, context_type: str = 'all', 
                        max_results: int = 10) -> List[RetrievalResult]:
        """Retrieve relevant context for a query"""
        results = []
        
        # Combine different retrieval methods
        if context_type in ['all', 'semantic']:
            results.extend(self._semantic_retrieval(query, max_results // 2))
        
        if context_type in ['all', 'keyword']:
            results.extend(self._keyword_retrieval(query, max_results // 2))
        
        if context_type in ['all', 'code']:
            results.extend(self._code_pattern_retrieval(query, max_results // 2))
        
        # Remove duplicates and sort by relevance
        unique_results = self._deduplicate_results(results)
        unique_results.sort(key=lambda x: x.relevance_score, reverse=True)
        
        return unique_results[:max_results]
    
    def _semantic_retrieval(self, query: str, max_results: int) -> List[RetrievalResult]:
        """Retrieve context using semantic similarity"""
        results = []
        
        if not SKLEARN_AVAILABLE or self.tfidf_matrix is None:
            return results
        
        try:
            # Use TF-IDF for semantic similarity
            query_vector = self.tfidf_vectorizer.transform([query])
            similarities = cosine_similarity(query_vector, self.tfidf_matrix).flatten()
            
            # Get top results
            top_indices = similarities.argsort()[-max_results:][::-1]
            
            for idx in top_indices:
                if similarities[idx] > 0.1:  # Minimum similarity threshold
                    chunk = self.document_chunks[idx]
                    
                    results.append(RetrievalResult(
                        content=chunk['content'],
                        source=chunk['file_path'],
                        relevance_score=float(similarities[idx]),
                        context_type='semantic',
                        metadata={
                            'language': chunk['language'],
                            'file_id': chunk['file_id']
                        },
                        line_numbers=(chunk['start_line'], chunk['end_line'])
                    ))
        
        except Exception as e:
            print(f"Error in semantic retrieval: {e}")
        
        return results
    
    def _keyword_retrieval(self, query: str, max_results: int) -> List[RetrievalResult]:
        """Retrieve context using keyword matching"""
        results = []
        cursor = self.conn.cursor()
        
        # Search in code elements
        cursor.execute('''
            SELECT e.name, e.type, e.signature, e.docstring, e.line_start, e.line_end,
                   f.path, f.language
            FROM elements e
            JOIN files f ON e.file_id = f.id
            WHERE e.name LIKE ? OR e.signature LIKE ? OR e.docstring LIKE ?
            ORDER BY 
                CASE 
                    WHEN e.name = ? THEN 1
                    WHEN e.name LIKE ? THEN 2
                    ELSE 3
                END,
                e.name
            LIMIT ?
        ''', (f'%{query}%', f'%{query}%', f'%{query}%', query, f'{query}%', max_results))
        
        for row in cursor.fetchall():
            name, elem_type, signature, docstring, line_start, line_end, file_path, language = row
            
            # Calculate relevance score
            relevance = self._calculate_keyword_relevance(query, name, signature, docstring)
            
            content = f"{elem_type} {name}"
            if signature:
                content += f"\n{signature}"
            if docstring:
                content += f"\n\"\"\"{docstring}\"\"\""
            
            results.append(RetrievalResult(
                content=content,
                source=file_path,
                relevance_score=relevance,
                context_type='keyword',
                metadata={
                    'element_type': elem_type,
                    'element_name': name,
                    'language': language
                },
                line_numbers=(line_start, line_end)
            ))
        
        return results
    
    def _code_pattern_retrieval(self, query: str, max_results: int) -> List[RetrievalResult]:
        """Retrieve context using code pattern matching"""
        results = []
        
        # Analyze query for code patterns
        patterns_found = {}
        for pattern_name, pattern in self.code_patterns.items():
            matches = pattern.findall(query)
            if matches:
                patterns_found[pattern_name] = matches
        
        if not patterns_found:
            return results
        
        cursor = self.conn.cursor()
        
        # Search for function calls
        if 'function_call' in patterns_found:
            for func_name in patterns_found['function_call']:
                cursor.execute('''
                    SELECT e.name, e.signature, e.line_start, e.line_end, f.path, f.language
                    FROM elements e
                    JOIN files f ON e.file_id = f.id
                    WHERE e.type = 'function' AND e.name = ?
                    LIMIT 5
                ''', (func_name,))
                
                for row in cursor.fetchall():
                    name, signature, line_start, line_end, file_path, language = row
                    
                    results.append(RetrievalResult(
                        content=f"function {name}\n{signature or ''}",
                        source=file_path,
                        relevance_score=0.9,  # High relevance for exact function matches
                        context_type='code_pattern',
                        metadata={
                            'pattern_type': 'function_definition',
                            'function_name': name,
                            'language': language
                        },
                        line_numbers=(line_start, line_end)
                    ))
        
        # Search for class usage
        if 'class_usage' in patterns_found:
            for class_name, method_name in patterns_found['class_usage']:
                cursor.execute('''
                    SELECT e.name, e.signature, e.line_start, e.line_end, f.path, f.language
                    FROM elements e
                    JOIN files f ON e.file_id = f.id
                    WHERE e.type = 'class' AND e.name = ?
                    LIMIT 3
                ''', (class_name,))
                
                for row in cursor.fetchall():
                    name, signature, line_start, line_end, file_path, language = row
                    
                    results.append(RetrievalResult(
                        content=f"class {name}\n{signature or ''}",
                        source=file_path,
                        relevance_score=0.8,
                        context_type='code_pattern',
                        metadata={
                            'pattern_type': 'class_definition',
                            'class_name': name,
                            'language': language
                        },
                        line_numbers=(line_start, line_end)
                    ))
        
        return results
    
    def _calculate_keyword_relevance(self, query: str, name: str, 
                                   signature: str = None, docstring: str = None) -> float:
        """Calculate relevance score for keyword matching"""
        score = 0.0
        query_lower = query.lower()
        
        # Exact name match
        if name.lower() == query_lower:
            score += 1.0
        elif query_lower in name.lower():
            score += 0.8
        
        # Signature match
        if signature and query_lower in signature.lower():
            score += 0.6
        
        # Docstring match
        if docstring and query_lower in docstring.lower():
            score += 0.4
        
        # Fuzzy matching (simple)
        if self._fuzzy_match(query_lower, name.lower()):
            score += 0.3
        
        return min(score, 1.0)
    
    def _fuzzy_match(self, query: str, target: str, threshold: float = 0.7) -> bool:
        """Simple fuzzy matching"""
        if len(query) == 0 or len(target) == 0:
            return False
        
        # Calculate Jaccard similarity
        query_chars = set(query)
        target_chars = set(target)
        
        intersection = len(query_chars & target_chars)
        union = len(query_chars | target_chars)
        
        similarity = intersection / union if union > 0 else 0
        return similarity >= threshold
    
    def _deduplicate_results(self, results: List[RetrievalResult]) -> List[RetrievalResult]:
        """Remove duplicate results based on content similarity"""
        unique_results = []
        seen_hashes = set()
        
        for result in results:
            content_hash = hashlib.md5(result.content.encode()).hexdigest()
            
            if content_hash not in seen_hashes:
                seen_hashes.add(content_hash)
                unique_results.append(result)
        
        return unique_results
    
    def get_file_context(self, file_path: str) -> Optional[CodeContext]:
        """Get comprehensive context for a specific file"""
        cursor = self.conn.cursor()
        
        # Get file info
        cursor.execute('SELECT id, language FROM files WHERE path = ?', (file_path,))
        file_row = cursor.fetchone()
        
        if not file_row:
            return None
        
        file_id, language = file_row
        
        # Get functions and classes
        cursor.execute('''
            SELECT name, type FROM elements 
            WHERE file_id = ? AND type IN ('function', 'class')
        ''', (file_id,))
        
        functions = []
        classes = []
        for name, elem_type in cursor.fetchall():
            if elem_type == 'function':
                functions.append(name)
            else:
                classes.append(name)
        
        # Get imports
        cursor.execute('''
            SELECT dependency_path FROM dependencies 
            WHERE file_id = ? AND dependency_type = 'import'
        ''', (file_id,))
        
        imports = [row[0] for row in cursor.fetchall()]
        
        # Calculate complexity score
        cursor.execute('''
            SELECT AVG(complexity) FROM elements 
            WHERE file_id = ? AND complexity > 0
        ''', (file_id,))
        
        complexity_result = cursor.fetchone()
        complexity_score = complexity_result[0] if complexity_result[0] else 1.0
        
        return CodeContext(
            file_path=file_path,
            function_name=functions[0] if functions else None,
            class_name=classes[0] if classes else None,
            imports=imports,
            dependencies=imports,  # Simplified
            related_files=[],  # TODO: Implement related file detection
            complexity_score=complexity_score,
            language=language
        )
    
    def get_related_code(self, element_name: str, element_type: str = None) -> List[RetrievalResult]:
        """Get code related to a specific element"""
        results = []
        cursor = self.conn.cursor()
        
        # Find the element
        query = 'SELECT file_id, line_start, line_end FROM elements WHERE name = ?'
        params = [element_name]
        
        if element_type:
            query += ' AND type = ?'
            params.append(element_type)
        
        cursor.execute(query, params)
        element_row = cursor.fetchone()
        
        if not element_row:
            return results
        
        file_id, line_start, line_end = element_row
        
        # Get file path
        cursor.execute('SELECT path, language FROM files WHERE id = ?', (file_id,))
        file_path, language = cursor.fetchone()
        
        # Find related elements in the same file
        cursor.execute('''
            SELECT name, type, signature, line_start, line_end
            FROM elements
            WHERE file_id = ? AND name != ?
            ORDER BY ABS(line_start - ?)
            LIMIT 5
        ''', (file_id, element_name, line_start))
        
        for row in cursor.fetchall():
            name, elem_type, signature, rel_line_start, rel_line_end = row
            
            results.append(RetrievalResult(
                content=f"{elem_type} {name}\n{signature or ''}",
                source=file_path,
                relevance_score=0.7,
                context_type='related_code',
                metadata={
                    'element_type': elem_type,
                    'element_name': name,
                    'language': language,
                    'distance': abs(line_start - rel_line_start)
                },
                line_numbers=(rel_line_start, rel_line_end)
            ))
        
        return results

class RAGEngine:
    """Main RAG engine that combines retrieval and generation context"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.retriever = ContextRetriever(db_path)
        self.context_cache = {}
        self.max_context_length = 8000  # Maximum context length for LLM
    
    def get_context_for_query(self, query: str, query_type: str = 'general') -> Dict[str, Any]:
        """Get comprehensive context for a query"""
        # Check cache first
        cache_key = hashlib.md5(f"{query}:{query_type}".encode()).hexdigest()
        if cache_key in self.context_cache:
            return self.context_cache[cache_key]
        
        context = {
            'query': query,
            'query_type': query_type,
            'retrieved_context': [],
            'code_context': None,
            'related_files': [],
            'suggestions': [],
            'total_relevance': 0.0
        }
        
        # Retrieve relevant context
        if query_type == 'code_modification':
            context['retrieved_context'] = self.retriever.retrieve_context(
                query, context_type='code', max_results=15
            )
        elif query_type == 'code_understanding':
            context['retrieved_context'] = self.retriever.retrieve_context(
                query, context_type='all', max_results=10
            )
        else:
            context['retrieved_context'] = self.retriever.retrieve_context(
                query, context_type='semantic', max_results=8
            )
        
        # Calculate total relevance
        context['total_relevance'] = sum(
            result.relevance_score for result in context['retrieved_context']
        )
        
        # Get file context if query mentions specific files
        file_mentions = self._extract_file_mentions(query)
        for file_path in file_mentions:
            file_context = self.retriever.get_file_context(file_path)
            if file_context:
                context['code_context'] = file_context
                break
        
        # Generate suggestions based on context
        context['suggestions'] = self._generate_suggestions(context)
        
        # Cache the result
        self.context_cache[cache_key] = context
        
        return context
    
    def format_context_for_llm(self, context: Dict[str, Any]) -> str:
        """Format context for LLM consumption"""
        formatted_context = []
        
        # Add query information
        formatted_context.append(f"Query: {context['query']}")
        formatted_context.append(f"Query Type: {context['query_type']}")
        formatted_context.append("")
        
        # Add retrieved context
        if context['retrieved_context']:
            formatted_context.append("Relevant Code Context:")
            for i, result in enumerate(context['retrieved_context'][:10]):  # Limit to top 10
                formatted_context.append(f"\n{i+1}. Source: {result.source}")
                if result.line_numbers:
                    formatted_context.append(f"   Lines: {result.line_numbers[0]}-{result.line_numbers[1]}")
                formatted_context.append(f"   Relevance: {result.relevance_score:.2f}")
                formatted_context.append(f"   Type: {result.context_type}")
                formatted_context.append(f"   Content:\n   {result.content}")
                formatted_context.append("")
        
        # Add file context
        if context['code_context']:
            cc = context['code_context']
            formatted_context.append("Current File Context:")
            formatted_context.append(f"File: {cc.file_path}")
            formatted_context.append(f"Language: {cc.language}")
            formatted_context.append(f"Complexity Score: {cc.complexity_score:.2f}")
            
            if cc.imports:
                formatted_context.append(f"Imports: {', '.join(cc.imports[:5])}")
            
            if cc.function_name:
                formatted_context.append(f"Current Function: {cc.function_name}")
            
            if cc.class_name:
                formatted_context.append(f"Current Class: {cc.class_name}")
            
            formatted_context.append("")
        
        # Add suggestions
        if context['suggestions']:
            formatted_context.append("Suggestions:")
            for suggestion in context['suggestions'][:3]:  # Limit to top 3
                formatted_context.append(f"- {suggestion}")
            formatted_context.append("")
        
        # Join and truncate if necessary
        full_context = '\n'.join(formatted_context)
        
        if len(full_context) > self.max_context_length:
            # Truncate while preserving structure
            truncated = full_context[:self.max_context_length]
            last_newline = truncated.rfind('\n')
            if last_newline > 0:
                truncated = truncated[:last_newline]
            full_context = truncated + "\n\n[Context truncated due to length]"
        
        return full_context
    
    def _extract_file_mentions(self, query: str) -> List[str]:
        """Extract file paths mentioned in the query"""
        file_patterns = [
            r'([a-zA-Z_][a-zA-Z0-9_]*\.py)',
            r'([a-zA-Z_][a-zA-Z0-9_]*\.js)',
            r'([a-zA-Z_][a-zA-Z0-9_]*\.ts)',
            r'([a-zA-Z_][a-zA-Z0-9_]*\.java)',
            r'([a-zA-Z_][a-zA-Z0-9_]*\.cpp)',
            r'([a-zA-Z_][a-zA-Z0-9_]*\.c)',
            r'([a-zA-Z_][a-zA-Z0-9_]*\.h)',
        ]
        
        file_mentions = []
        for pattern in file_patterns:
            matches = re.findall(pattern, query)
            file_mentions.extend(matches)
        
        return file_mentions
    
    def _generate_suggestions(self, context: Dict[str, Any]) -> List[str]:
        """Generate suggestions based on context"""
        suggestions = []
        
        # Analyze retrieved context for patterns
        context_types = Counter(
            result.context_type for result in context['retrieved_context']
        )
        
        if context_types.get('code_pattern', 0) > 0:
            suggestions.append("Consider reviewing the function definitions and their usage patterns")
        
        if context_types.get('semantic', 0) > 2:
            suggestions.append("Multiple related code sections found - consider refactoring for better organization")
        
        if context['code_context'] and context['code_context'].complexity_score > 5:
            suggestions.append("High complexity detected - consider breaking down into smaller functions")
        
        if context['total_relevance'] < 2.0:
            suggestions.append("Limited relevant context found - consider providing more specific details")
        
        return suggestions
    
    def clear_cache(self):
        """Clear the context cache"""
        self.context_cache.clear()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            'cache_size': len(self.context_cache),
            'cache_keys': list(self.context_cache.keys())
        }

def main():
    """Main entry point for testing"""
    import argparse
    
    parser = argparse.ArgumentParser(description='RAG Engine Test')
    parser.add_argument('db_path', help='Path to the database file')
    parser.add_argument('--query', help='Test query')
    parser.add_argument('--type', default='general', help='Query type')
    
    args = parser.parse_args()
    
    # Initialize RAG engine
    rag = RAGEngine(args.db_path)
    
    if args.query:
        # Test query
        context = rag.get_context_for_query(args.query, args.type)
        formatted = rag.format_context_for_llm(context)
        
        print("Context for query:", args.query)
        print("=" * 50)
        print(formatted)
        print("=" * 50)
        print(f"Total relevance: {context['total_relevance']:.2f}")
        print(f"Results count: {len(context['retrieved_context'])}")

if __name__ == '__main__':
    main()
