# Core dependencies for Augura Coder Agent
aiohttp>=3.8.0
asyncio-mqtt>=0.11.0
websockets>=11.0.0

# AI and ML libraries
openai>=1.0.0
anthropic>=0.7.0

# Data processing
numpy>=1.24.0
pandas>=2.0.0

# Text processing and embeddings
sentence-transformers>=2.2.0
scikit-learn>=1.3.0
nltk>=3.8.0
spacy>=3.6.0

# Code analysis
tree-sitter>=0.20.0
tree-sitter-python>=0.20.0
tree-sitter-javascript>=0.20.0
tree-sitter-typescript>=0.20.0

# Database
sqlite3  # Built-in with Python

# File processing
chardet>=5.0.0
python-magic>=0.4.27

# Configuration and utilities
python-dotenv>=1.0.0
pydantic>=2.0.0
click>=8.0.0

# Development tools
pytest>=7.0.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.0.0

# Optional dependencies for enhanced features
# Uncomment if needed:
# torch>=2.0.0  # For advanced ML models
# transformers>=4.30.0  # For Hugging Face models
# chromadb>=0.4.0  # For vector database
# langchain>=0.1.0  # For LangChain integration
# faiss-cpu>=1.7.0  # For similarity search
