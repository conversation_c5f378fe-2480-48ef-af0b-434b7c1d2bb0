const vscode = require('vscode');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');

// Import managers
const ProjectIndexer = require('./src/indexer/ProjectIndexer');
const CodeAnalyzer = require('./src/analyzer/CodeAnalyzer');
const AIAgent = require('./src/agent/AIAgent');
const MemoryManager = require('./src/memory/MemoryManager');
const ChatProvider = require('./src/webview/ChatProvider');

class AuguraExtension {
    constructor(context) {
        this.context = context;
        this.projectIndexer = new ProjectIndexer(context);
        this.codeAnalyzer = new CodeAnalyzer();
        this.memoryManager = new MemoryManager(context);
        this.aiAgent = new AIAgent(context);
        this.chatProvider = null;
        
        this.initialize();
    }

    async initialize() {
        console.log('Augura Agent: Initializing...');
        
        // Initialize memory manager
        await this.memoryManager.initialize();
        
        // Initialize AI agent
        await this.aiAgent.initialize();
        
        // Register commands
        this.registerCommands();
        
        // Register providers
        this.registerProviders();
        
        // Start file watcher
        this.startFileWatcher();
        
        // Auto-index project if enabled
        if (vscode.workspace.getConfiguration('augura').get('indexingEnabled')) {
            await this.indexCurrentProject();
        }
        
        console.log('Augura Agent: Initialized successfully');
    }

    registerCommands() {
        // Open chat command
        const openChatCommand = vscode.commands.registerCommand('augura.openChat', () => {
            this.openChatView();
        });

        // Index project command
        const indexProjectCommand = vscode.commands.registerCommand('augura.indexProject', async () => {
            await this.indexCurrentProject();
        });

        // Analyze file command
        const analyzeFileCommand = vscode.commands.registerCommand('augura.analyzeFile', async () => {
            await this.analyzeCurrentFile();
        });

        // Refactor code command
        const refactorCodeCommand = vscode.commands.registerCommand('augura.refactorCode', async () => {
            await this.refactorSelectedCode();
        });

        this.context.subscriptions.push(
            openChatCommand,
            indexProjectCommand,
            analyzeFileCommand,
            refactorCodeCommand
        );
    }

    registerProviders() {
        // Register chat webview provider
        this.chatProvider = new ChatProvider(this.context, this.aiAgent);
        
        const chatViewProvider = vscode.window.registerWebviewViewProvider(
            'augura.chatView',
            this.chatProvider,
            {
                webviewOptions: {
                    retainContextWhenHidden: true
                }
            }
        );

        this.context.subscriptions.push(chatViewProvider);
    }

    startFileWatcher() {
        if (!vscode.workspace.workspaceFolders) return;

        const workspaceFolder = vscode.workspace.workspaceFolders[0];
        const watcher = vscode.workspace.createFileSystemWatcher(
            new vscode.RelativePattern(workspaceFolder, '**/*'),
            false, // ignoreCreateEvents
            false, // ignoreChangeEvents
            false  // ignoreDeleteEvents
        );

        watcher.onDidCreate(async (uri) => {
            await this.handleFileChange('create', uri);
        });

        watcher.onDidChange(async (uri) => {
            await this.handleFileChange('change', uri);
        });

        watcher.onDidDelete(async (uri) => {
            await this.handleFileChange('delete', uri);
        });

        this.context.subscriptions.push(watcher);
    }

    async handleFileChange(type, uri) {
        try {
            const relativePath = vscode.workspace.asRelativePath(uri);
            
            // Skip certain files/directories
            if (this.shouldIgnoreFile(relativePath)) return;

            console.log(`File ${type}: ${relativePath}`);

            switch (type) {
                case 'create':
                case 'change':
                    await this.projectIndexer.indexFile(uri.fsPath);
                    break;
                case 'delete':
                    await this.projectIndexer.removeFile(uri.fsPath);
                    break;
            }

            // Update memory with file changes
            await this.memoryManager.updateFileChange(type, uri.fsPath);

        } catch (error) {
            console.error('Error handling file change:', error);
        }
    }

    shouldIgnoreFile(relativePath) {
        const ignorePatterns = [
            'node_modules',
            '.git',
            '.vscode',
            'dist',
            'build',
            '*.log',
            '*.tmp',
            '.DS_Store'
        ];

        return ignorePatterns.some(pattern => {
            if (pattern.includes('*')) {
                const regex = new RegExp(pattern.replace(/\*/g, '.*'));
                return regex.test(relativePath);
            }
            return relativePath.includes(pattern);
        });
    }

    async indexCurrentProject() {
        if (!vscode.workspace.workspaceFolders) {
            vscode.window.showWarningMessage('No workspace folder open');
            return;
        }

        const workspaceFolder = vscode.workspace.workspaceFolders[0];
        
        vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: "Indexing project...",
            cancellable: true
        }, async (progress, token) => {
            try {
                await this.projectIndexer.indexProject(workspaceFolder.uri.fsPath, progress, token);
                vscode.window.showInformationMessage('Project indexed successfully');
            } catch (error) {
                console.error('Error indexing project:', error);
                vscode.window.showErrorMessage('Failed to index project: ' + error.message);
            }
        });
    }

    async analyzeCurrentFile() {
        const activeEditor = vscode.window.activeTextEditor;
        if (!activeEditor) {
            vscode.window.showWarningMessage('No active file');
            return;
        }

        const document = activeEditor.document;
        const analysis = await this.codeAnalyzer.analyzeFile(document.uri.fsPath, document.getText());
        
        // Show analysis in chat
        if (this.chatProvider) {
            await this.chatProvider.showAnalysis(analysis);
        }
    }

    async refactorSelectedCode() {
        const activeEditor = vscode.window.activeTextEditor;
        if (!activeEditor) {
            vscode.window.showWarningMessage('No active file');
            return;
        }

        const selection = activeEditor.selection;
        if (selection.isEmpty) {
            vscode.window.showWarningMessage('No code selected');
            return;
        }

        const selectedText = activeEditor.document.getText(selection);
        const filePath = activeEditor.document.uri.fsPath;

        // Get refactoring suggestions from AI
        const suggestions = await this.aiAgent.getRefactoringSuggestions(filePath, selectedText);
        
        // Show suggestions in chat
        if (this.chatProvider) {
            await this.chatProvider.showRefactoringSuggestions(suggestions);
        }
    }

    openChatView() {
        vscode.commands.executeCommand('workbench.view.extension.augura-agent');
    }

    dispose() {
        // Cleanup resources
        if (this.projectIndexer) {
            this.projectIndexer.dispose();
        }
        if (this.memoryManager) {
            this.memoryManager.dispose();
        }
    }
}

let extension;

function activate(context) {
    console.log('Augura Agent extension is now active!');
    extension = new AuguraExtension(context);
}

function deactivate() {
    if (extension) {
        extension.dispose();
    }
}

module.exports = {
    activate,
    deactivate
};
