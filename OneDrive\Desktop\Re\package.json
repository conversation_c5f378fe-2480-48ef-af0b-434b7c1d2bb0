{"name": "augura-coder-agent", "displayName": "Augura Coder Agent", "description": "Advanced AI Programming Agent with Codebase Understanding", "version": "1.0.0", "publisher": "augura", "repository": {"type": "git", "url": "https://github.com/augura/augura-coder-agent.git"}, "homepage": "https://github.com/augura/augura-coder-agent#readme", "bugs": {"url": "https://github.com/augura/augura-coder-agent/issues"}, "license": "MIT", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Machine Learning", "Programming Languages"], "keywords": ["ai", "assistant", "programming", "codebase", "agent", "langchain", "rag"], "activationEvents": ["onStartupFinished"], "main": "./extension.js", "contributes": {"commands": [{"command": "augura.openChat", "title": "Open <PERSON><PERSON>", "icon": "$(comment-discussion)"}, {"command": "augura.startAgent", "title": "Start AI Agent", "icon": "$(play)"}, {"command": "augura.stopAgent", "title": "Stop AI Agent", "icon": "$(stop)"}, {"command": "augura.indexProject", "title": "Index Current Project", "icon": "$(search)"}, {"command": "augura.analyzeFile", "title": "Analyze Current File", "icon": "$(file-code)"}, {"command": "augura.refactorCode", "title": "Refactor Selected Code", "icon": "$(tools)"}, {"command": "augura.explainCode", "title": "Explain Selected Code", "icon": "$(question)"}, {"command": "augura.generateTests", "title": "Generate Tests", "icon": "$(beaker)"}, {"command": "augura.showStats", "title": "Show Agent Statistics", "icon": "$(graph)"}, {"command": "augura.clearMemory", "title": "Clear Agent Memory", "icon": "$(trash)"}], "viewsContainers": {"activitybar": [{"id": "augura-agent", "title": "Augura Agent", "icon": "$(robot)"}]}, "views": {"augura-agent": [{"type": "webview", "id": "augura.chatView", "name": "Cha<PERSON>", "when": "true"}, {"id": "augura.projectView", "name": "Project Analysis", "when": "true"}]}, "menus": {"editor/context": [{"command": "augura.analyzeFile", "group": "augura@1"}, {"command": "augura.explainCode", "group": "augura@2", "when": "editorHasSelection"}, {"command": "augura.refactorCode", "group": "augura@3", "when": "editorHasSelection"}, {"command": "augura.generateTests", "group": "augura@4"}], "explorer/context": [{"command": "augura.indexProject", "group": "augura@1"}], "commandPalette": [{"command": "augura.startAgent", "when": "true"}, {"command": "augura.stopAgent", "when": "true"}, {"command": "augura.showStats", "when": "true"}, {"command": "augura.clearMemory", "when": "true"}]}, "configuration": {"title": "Augura Agent", "properties": {"augura.openRouterApiKey": {"type": "string", "default": "", "description": "OpenRouter API Key"}, "augura.groqApiKey": {"type": "string", "default": "", "description": "Groq API Key"}, "augura.defaultModel": {"type": "string", "default": "openrouter/horizon-beta", "description": "Default AI Model"}, "augura.indexingEnabled": {"type": "boolean", "default": true, "description": "Enable automatic project indexing"}, "augura.maxFileSize": {"type": "number", "default": 1048576, "description": "Maximum file size to index (bytes)"}, "augura.memoryEnabled": {"type": "boolean", "default": true, "description": "Enable agent memory and learning"}, "augura.ragEnabled": {"type": "boolean", "default": true, "description": "Enable RAG (Retrieval-Augmented Generation)"}, "augura.mcpEnabled": {"type": "boolean", "default": true, "description": "Enable MCP (Model Context Protocol) server"}, "augura.mcpPort": {"type": "number", "default": 8765, "description": "MCP server port"}, "augura.debugMode": {"type": "boolean", "default": false, "description": "Enable debug mode for detailed logging"}, "augura.autoIndex": {"type": "boolean", "default": true, "description": "Automatically index project on startup"}, "augura.maxTokens": {"type": "number", "default": 4000, "description": "Maximum tokens for AI responses"}, "augura.temperature": {"type": "number", "default": 0.7, "minimum": 0, "maximum": 2, "description": "AI model temperature (creativity level)"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "echo 'Compilation complete - no TypeScript to compile'", "watch": "echo 'Watch mode - no TypeScript to watch'", "test": "echo 'No tests configured'", "package": "vsce package"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "^18.0.0"}, "dependencies": {"axios": "^1.6.0", "chokidar": "^3.5.3", "ignore": "^5.3.0", "node-fetch": "^3.3.2"}}