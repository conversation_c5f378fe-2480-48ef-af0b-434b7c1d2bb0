{"name": "augura-coder-agent", "displayName": "Augura Coder Agent", "description": "Advanced AI Programming Agent with Codebase Understanding", "version": "1.0.0", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Machine Learning", "Programming Languages"], "keywords": ["ai", "assistant", "programming", "codebase", "agent", "langchain", "rag"], "activationEvents": ["onStartupFinished"], "main": "./extension.js", "contributes": {"commands": [{"command": "augura.openChat", "title": "Open <PERSON><PERSON>", "icon": "$(comment-discussion)"}, {"command": "augura.indexProject", "title": "Index Current Project", "icon": "$(search)"}, {"command": "augura.analyzeFile", "title": "Analyze Current File", "icon": "$(file-code)"}, {"command": "augura.refactorCode", "title": "Refactor Selected Code", "icon": "$(tools)"}], "viewsContainers": {"activitybar": [{"id": "augura-agent", "title": "Augura Agent", "icon": "$(robot)"}]}, "views": {"augura-agent": [{"type": "webview", "id": "augura.chatView", "name": "Cha<PERSON>", "when": "true"}, {"id": "augura.projectView", "name": "Project Analysis", "when": "true"}]}, "menus": {"editor/context": [{"command": "augura.analyzeFile", "group": "augura", "when": "editorHasSelection"}, {"command": "augura.refactorCode", "group": "augura", "when": "editorHasSelection"}], "explorer/context": [{"command": "augura.indexProject", "group": "augura"}]}, "configuration": {"title": "Augura Agent", "properties": {"augura.openRouterApiKey": {"type": "string", "default": "", "description": "OpenRouter API Key"}, "augura.groqApiKey": {"type": "string", "default": "", "description": "Groq API Key"}, "augura.defaultModel": {"type": "string", "default": "openrouter/horizon-beta", "description": "Default AI Model"}, "augura.indexingEnabled": {"type": "boolean", "default": true, "description": "Enable automatic project indexing"}, "augura.maxFileSize": {"type": "number", "default": 1048576, "description": "Maximum file size to index (bytes)"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "node ./node_modules/vscode/bin/compile", "watch": "node ./node_modules/vscode/bin/compile -watch", "test": "node ./node_modules/vscode/bin/test"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "^18.0.0"}, "dependencies": {"axios": "^1.6.0", "chokidar": "^3.5.3", "ignore": "^5.3.0", "node-fetch": "^3.3.2"}}