const vscode = require('vscode');
const fetch = require('node-fetch');

class AIAgent {
    constructor(context) {
        this.context = context;
        this.config = vscode.workspace.getConfiguration('augura');
        this.conversationHistory = [];
        this.currentTask = null;
        this.projectContext = null;
        this.isProcessing = false;
        
        // API configurations
        this.openRouterConfig = {
            baseUrl: 'https://openrouter.ai/api/v1/chat/completions',
            apiKey: this.config.get('openRouterApiKey'),
            model: 'openrouter/horizon-beta'
        };
        
        this.groqConfig = {
            baseUrl: 'https://api.groq.com/openai/v1/chat/completions',
            apiKey: this.config.get('groqApiKey'),
            model: 'qwen/qwen3-32b'
        };
    }

    async initialize() {
        console.log('Initializing AI Agent...');
        
        // Load project context
        await this.loadProjectContext();
        
        // Initialize conversation with system prompt
        this.conversationHistory = [{
            role: 'system',
            content: this.getSystemPrompt()
        }];
        
        console.log('AI Agent initialized successfully');
    }

    getSystemPrompt() {
        return `You are Augura Coder, an advanced AI programming assistant integrated into VS Code. You have deep understanding of the current codebase and can:

1. **Code Analysis & Understanding**: Analyze code structure, dependencies, and relationships
2. **Intelligent Code Modification**: Make precise edits without breaking existing functionality
3. **Dependency Management**: Update related files when changes affect dependencies
4. **File Operations**: Create, modify, move, and delete files as needed
5. **Refactoring**: Improve code structure while maintaining functionality
6. **Bug Detection**: Identify and fix potential issues
7. **Code Generation**: Create new code that integrates seamlessly with existing codebase

**Current Project Context:**
${this.projectContext ? JSON.stringify(this.projectContext, null, 2) : 'No project loaded'}

**Guidelines:**
- Always consider the impact of changes on the entire codebase
- Maintain code style and conventions used in the project
- Provide clear explanations for your actions
- Ask for clarification when requirements are ambiguous
- Suggest improvements and best practices
- Be precise and avoid unnecessary changes

You can perform file operations, analyze code, and make intelligent modifications. Always explain what you're doing and why.`;
    }

    async loadProjectContext() {
        try {
            if (!vscode.workspace.workspaceFolders) {
                this.projectContext = null;
                return;
            }

            const workspaceFolder = vscode.workspace.workspaceFolders[0];
            const packageJsonPath = vscode.Uri.joinPath(workspaceFolder.uri, 'package.json');
            
            try {
                const packageJsonContent = await vscode.workspace.fs.readFile(packageJsonPath);
                const packageJson = JSON.parse(packageJsonContent.toString());
                
                this.projectContext = {
                    name: packageJson.name || 'Unknown Project',
                    version: packageJson.version || '1.0.0',
                    description: packageJson.description || '',
                    dependencies: packageJson.dependencies || {},
                    devDependencies: packageJson.devDependencies || {},
                    scripts: packageJson.scripts || {},
                    workspacePath: workspaceFolder.uri.fsPath
                };
            } catch (error) {
                // No package.json found, create basic context
                this.projectContext = {
                    name: workspaceFolder.name,
                    workspacePath: workspaceFolder.uri.fsPath,
                    type: 'unknown'
                };
            }
        } catch (error) {
            console.error('Error loading project context:', error);
            this.projectContext = null;
        }
    }

    async processMessage(message, options = {}) {
        if (this.isProcessing) {
            throw new Error('Agent is already processing a request');
        }

        this.isProcessing = true;
        
        try {
            // Add user message to history
            this.conversationHistory.push({
                role: 'user',
                content: message,
                timestamp: new Date().toISOString()
            });

            // Determine if this is a code-related task
            const taskType = this.analyzeTaskType(message);
            
            // Get relevant context based on task type
            const context = await this.getRelevantContext(message, taskType);
            
            // Prepare enhanced message with context
            const enhancedMessage = this.enhanceMessageWithContext(message, context);
            
            // Choose appropriate API based on task complexity
            const useGroq = taskType === 'simple' || options.useGroq;
            const response = await this.callAI(enhancedMessage, useGroq);
            
            // Process AI response and extract actions
            const processedResponse = await this.processAIResponse(response, taskType);
            
            // Add response to history
            this.conversationHistory.push({
                role: 'assistant',
                content: response,
                timestamp: new Date().toISOString(),
                taskType,
                actions: processedResponse.actions
            });

            return processedResponse;
            
        } finally {
            this.isProcessing = false;
        }
    }

    analyzeTaskType(message) {
        const messageLower = message.toLowerCase();
        
        // Code modification tasks
        if (messageLower.includes('refactor') || 
            messageLower.includes('modify') || 
            messageLower.includes('change') ||
            messageLower.includes('update') ||
            messageLower.includes('fix')) {
            return 'code_modification';
        }
        
        // File operations
        if (messageLower.includes('create file') ||
            messageLower.includes('new file') ||
            messageLower.includes('delete file') ||
            messageLower.includes('move file')) {
            return 'file_operation';
        }
        
        // Analysis tasks
        if (messageLower.includes('analyze') ||
            messageLower.includes('explain') ||
            messageLower.includes('understand') ||
            messageLower.includes('review')) {
            return 'analysis';
        }
        
        // Simple questions
        if (messageLower.includes('what') ||
            messageLower.includes('how') ||
            messageLower.includes('why') ||
            messageLower.includes('?')) {
            return 'simple';
        }
        
        return 'complex';
    }

    async getRelevantContext(message, taskType) {
        const context = {
            currentFile: null,
            selectedCode: null,
            relatedFiles: [],
            projectStructure: null
        };

        try {
            // Get current file context
            const activeEditor = vscode.window.activeTextEditor;
            if (activeEditor) {
                context.currentFile = {
                    path: activeEditor.document.uri.fsPath,
                    language: activeEditor.document.languageId,
                    content: activeEditor.document.getText()
                };

                // Get selected code if any
                const selection = activeEditor.selection;
                if (!selection.isEmpty) {
                    context.selectedCode = activeEditor.document.getText(selection);
                }
            }

            // Get project structure for complex tasks
            if (taskType === 'code_modification' || taskType === 'file_operation') {
                context.projectStructure = await this.getProjectStructure();
                context.relatedFiles = await this.findRelatedFiles(message);
            }

        } catch (error) {
            console.error('Error getting context:', error);
        }

        return context;
    }

    async getProjectStructure() {
        // This would integrate with the ProjectIndexer
        // For now, return a simplified structure
        try {
            if (!vscode.workspace.workspaceFolders) return null;
            
            const workspaceFolder = vscode.workspace.workspaceFolders[0];
            const files = await vscode.workspace.findFiles('**/*.{js,ts,py,java,cpp,c,h}', '**/node_modules/**');
            
            return {
                totalFiles: files.length,
                fileTypes: this.categorizeFiles(files),
                mainDirectories: await this.getMainDirectories(workspaceFolder.uri)
            };
        } catch (error) {
            console.error('Error getting project structure:', error);
            return null;
        }
    }

    categorizeFiles(files) {
        const categories = {};
        
        files.forEach(file => {
            const ext = file.path.split('.').pop();
            categories[ext] = (categories[ext] || 0) + 1;
        });
        
        return categories;
    }

    async getMainDirectories(workspaceUri) {
        try {
            const entries = await vscode.workspace.fs.readDirectory(workspaceUri);
            return entries
                .filter(([name, type]) => type === vscode.FileType.Directory && !name.startsWith('.'))
                .map(([name]) => name);
        } catch (error) {
            return [];
        }
    }

    async findRelatedFiles(message) {
        // Simple implementation - would be enhanced with ProjectIndexer
        const relatedFiles = [];
        
        try {
            const activeEditor = vscode.window.activeTextEditor;
            if (activeEditor) {
                const currentDir = vscode.Uri.joinPath(activeEditor.document.uri, '..');
                const files = await vscode.workspace.findFiles(
                    new vscode.RelativePattern(currentDir, '*.{js,ts,py,java}'),
                    null,
                    10
                );
                
                relatedFiles.push(...files.map(file => ({
                    path: file.fsPath,
                    relativePath: vscode.workspace.asRelativePath(file)
                })));
            }
        } catch (error) {
            console.error('Error finding related files:', error);
        }
        
        return relatedFiles;
    }

    enhanceMessageWithContext(message, context) {
        let enhancedMessage = message;
        
        if (context.currentFile) {
            enhancedMessage += `\n\n**Current File Context:**\n`;
            enhancedMessage += `File: ${context.currentFile.path}\n`;
            enhancedMessage += `Language: ${context.currentFile.language}\n`;
            
            if (context.selectedCode) {
                enhancedMessage += `\n**Selected Code:**\n\`\`\`${context.currentFile.language}\n${context.selectedCode}\n\`\`\`\n`;
            } else {
                // Include first 50 lines of current file for context
                const lines = context.currentFile.content.split('\n').slice(0, 50);
                enhancedMessage += `\n**File Content (first 50 lines):**\n\`\`\`${context.currentFile.language}\n${lines.join('\n')}\n\`\`\`\n`;
            }
        }
        
        if (context.projectStructure) {
            enhancedMessage += `\n\n**Project Structure:**\n`;
            enhancedMessage += `Total Files: ${context.projectStructure.totalFiles}\n`;
            enhancedMessage += `File Types: ${JSON.stringify(context.projectStructure.fileTypes)}\n`;
            enhancedMessage += `Main Directories: ${context.projectStructure.mainDirectories.join(', ')}\n`;
        }
        
        if (context.relatedFiles.length > 0) {
            enhancedMessage += `\n\n**Related Files:**\n`;
            context.relatedFiles.forEach(file => {
                enhancedMessage += `- ${file.relativePath}\n`;
            });
        }
        
        return enhancedMessage;
    }

    async callAI(message, useGroq = false) {
        const config = useGroq ? this.groqConfig : this.openRouterConfig;
        
        if (!config.apiKey) {
            throw new Error(`${useGroq ? 'Groq' : 'OpenRouter'} API key not configured`);
        }

        const messages = [
            ...this.conversationHistory,
            { role: 'user', content: message }
        ];

        try {
            const response = await fetch(config.baseUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${config.apiKey}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    model: config.model,
                    messages: messages,
                    temperature: 0.7,
                    max_tokens: 4000
                })
            });

            if (!response.ok) {
                throw new Error(`API request failed: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            return data.choices[0].message.content;

        } catch (error) {
            console.error('AI API call failed:', error);
            throw new Error(`Failed to get AI response: ${error.message}`);
        }
    }

    async processAIResponse(response, taskType) {
        const processedResponse = {
            content: response,
            actions: [],
            suggestions: [],
            codeChanges: []
        };

        try {
            // Extract code blocks
            const codeBlocks = this.extractCodeBlocks(response);
            
            // Extract file operations
            const fileOperations = this.extractFileOperations(response);
            
            // Extract suggestions
            const suggestions = this.extractSuggestions(response);

            processedResponse.actions = [...fileOperations];
            processedResponse.suggestions = suggestions;
            processedResponse.codeChanges = codeBlocks;

        } catch (error) {
            console.error('Error processing AI response:', error);
        }

        return processedResponse;
    }

    extractCodeBlocks(response) {
        const codeBlocks = [];
        const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
        let match;

        while ((match = codeBlockRegex.exec(response)) !== null) {
            codeBlocks.push({
                language: match[1] || 'text',
                code: match[2].trim(),
                startIndex: match.index,
                endIndex: match.index + match[0].length
            });
        }

        return codeBlocks;
    }

    extractFileOperations(response) {
        const operations = [];
        
        // Look for file operation patterns
        const patterns = [
            /create\s+(?:a\s+)?(?:new\s+)?file\s+[`"]?([^`"\s]+)[`"]?/gi,
            /modify\s+(?:the\s+)?file\s+[`"]?([^`"\s]+)[`"]?/gi,
            /update\s+(?:the\s+)?file\s+[`"]?([^`"\s]+)[`"]?/gi,
            /delete\s+(?:the\s+)?file\s+[`"]?([^`"\s]+)[`"]?/gi
        ];

        patterns.forEach((pattern, index) => {
            let match;
            while ((match = pattern.exec(response)) !== null) {
                const operationType = ['create', 'modify', 'update', 'delete'][index];
                operations.push({
                    type: operationType,
                    file: match[1],
                    description: match[0]
                });
            }
        });

        return operations;
    }

    extractSuggestions(response) {
        const suggestions = [];
        
        // Look for suggestion patterns
        const suggestionPatterns = [
            /(?:i\s+)?suggest(?:ion)?:?\s*(.+?)(?:\n|$)/gi,
            /(?:you\s+)?(?:should|could|might)\s+consider\s+(.+?)(?:\n|$)/gi,
            /recommendation:?\s*(.+?)(?:\n|$)/gi
        ];

        suggestionPatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(response)) !== null) {
                suggestions.push({
                    text: match[1].trim(),
                    type: 'general'
                });
            }
        });

        return suggestions;
    }

    async getRefactoringSuggestions(filePath, selectedCode) {
        const message = `Please analyze the following code and provide refactoring suggestions:

**File:** ${filePath}
**Selected Code:**
\`\`\`
${selectedCode}
\`\`\`

Please provide:
1. Code quality assessment
2. Potential improvements
3. Refactored version if applicable
4. Explanation of changes`;

        return await this.processMessage(message, { useGroq: true });
    }

    async explainCode(filePath, code) {
        const message = `Please explain the following code:

**File:** ${filePath}
**Code:**
\`\`\`
${code}
\`\`\`

Please provide:
1. What this code does
2. How it works
3. Key components and their roles
4. Any potential issues or improvements`;

        return await this.processMessage(message, { useGroq: true });
    }

    async generateCode(requirements, context = {}) {
        let message = `Please generate code based on the following requirements:

**Requirements:**
${requirements}`;

        if (context.language) {
            message += `\n**Language:** ${context.language}`;
        }

        if (context.framework) {
            message += `\n**Framework:** ${context.framework}`;
        }

        if (context.existingCode) {
            message += `\n**Existing Code Context:**
\`\`\`
${context.existingCode}
\`\`\``;
        }

        message += `\n\nPlease provide:
1. Complete, working code
2. Explanation of the implementation
3. Usage examples
4. Any dependencies or setup required`;

        return await this.processMessage(message);
    }

    // Streaming support for real-time responses
    async processMessageStream(message, onChunk, options = {}) {
        if (this.isProcessing) {
            throw new Error('Agent is already processing a request');
        }

        this.isProcessing = true;
        
        try {
            const taskType = this.analyzeTaskType(message);
            const context = await this.getRelevantContext(message, taskType);
            const enhancedMessage = this.enhanceMessageWithContext(message, context);
            
            const config = options.useGroq ? this.groqConfig : this.openRouterConfig;
            
            const messages = [
                ...this.conversationHistory,
                { role: 'user', content: enhancedMessage }
            ];

            const response = await fetch(config.baseUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${config.apiKey}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    model: config.model,
                    messages: messages,
                    temperature: 0.7,
                    max_tokens: 4000,
                    stream: true
                })
            });

            if (!response.ok) {
                throw new Error(`API request failed: ${response.status}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';
            let fullResponse = '';

            try {
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    buffer += decoder.decode(value, { stream: true });

                    while (true) {
                        const lineEnd = buffer.indexOf('\n');
                        if (lineEnd === -1) break;

                        const line = buffer.slice(0, lineEnd).trim();
                        buffer = buffer.slice(lineEnd + 1);

                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            if (data === '[DONE]') break;

                            try {
                                const parsed = JSON.parse(data);
                                const content = parsed.choices[0].delta.content;
                                if (content) {
                                    fullResponse += content;
                                    onChunk(content);
                                }
                            } catch (e) {
                                // Ignore invalid JSON
                            }
                        }
                    }
                }
            } finally {
                reader.cancel();
            }

            // Add to conversation history
            this.conversationHistory.push({
                role: 'user',
                content: message,
                timestamp: new Date().toISOString()
            });

            this.conversationHistory.push({
                role: 'assistant',
                content: fullResponse,
                timestamp: new Date().toISOString(),
                taskType
            });

            return await this.processAIResponse(fullResponse, taskType);

        } finally {
            this.isProcessing = false;
        }
    }

    clearHistory() {
        this.conversationHistory = [{
            role: 'system',
            content: this.getSystemPrompt()
        }];
    }

    getHistory() {
        return this.conversationHistory.filter(msg => msg.role !== 'system');
    }

    updateConfig() {
        this.config = vscode.workspace.getConfiguration('augura');
        this.openRouterConfig.apiKey = this.config.get('openRouterApiKey');
        this.groqConfig.apiKey = this.config.get('groqApiKey');
    }
}

module.exports = AIAgent;
