const fs = require('fs').promises;
const path = require('path');

class CodeAnalyzer {
    constructor() {
        this.analysisCache = new Map();
    }

    async analyzeFile(filePath, content) {
        const cacheKey = `${filePath}:${this.getContentHash(content)}`;
        
        if (this.analysisCache.has(cacheKey)) {
            return this.analysisCache.get(cacheKey);
        }

        const analysis = await this.performAnalysis(filePath, content);
        this.analysisCache.set(cacheKey, analysis);
        
        return analysis;
    }

    async performAnalysis(filePath, content) {
        const ext = path.extname(filePath);
        const language = this.detectLanguage(ext);
        
        const analysis = {
            filePath,
            language,
            metrics: this.calculateMetrics(content),
            structure: this.analyzeStructure(content, language),
            dependencies: this.analyzeDependencies(content, language),
            complexity: this.calculateComplexity(content, language),
            issues: this.detectIssues(content, language),
            suggestions: []
        };

        // Generate suggestions based on analysis
        analysis.suggestions = this.generateSuggestions(analysis);

        return analysis;
    }

    calculateMetrics(content) {
        const lines = content.split('\n');
        const nonEmptyLines = lines.filter(line => line.trim().length > 0);
        const commentLines = lines.filter(line => {
            const trimmed = line.trim();
            return trimmed.startsWith('//') || 
                   trimmed.startsWith('/*') || 
                   trimmed.startsWith('*') ||
                   trimmed.startsWith('#');
        });

        return {
            totalLines: lines.length,
            codeLines: nonEmptyLines.length - commentLines.length,
            commentLines: commentLines.length,
            blankLines: lines.length - nonEmptyLines.length,
            averageLineLength: nonEmptyLines.reduce((sum, line) => sum + line.length, 0) / nonEmptyLines.length || 0,
            longestLine: Math.max(...lines.map(line => line.length))
        };
    }

    analyzeStructure(content, language) {
        const structure = {
            functions: [],
            classes: [],
            variables: [],
            imports: [],
            exports: [],
            blocks: []
        };

        switch (language) {
            case 'javascript':
            case 'typescript':
                return this.analyzeJavaScriptStructure(content);
            case 'python':
                return this.analyzePythonStructure(content);
            case 'java':
                return this.analyzeJavaStructure(content);
            default:
                return structure;
        }
    }

    analyzeJavaScriptStructure(content) {
        const structure = {
            functions: [],
            classes: [],
            variables: [],
            imports: [],
            exports: [],
            blocks: []
        };

        const lines = content.split('\n');
        let currentClass = null;
        let braceLevel = 0;
        let currentFunction = null;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const trimmed = line.trim();

            // Count braces for nesting level
            braceLevel += (line.match(/{/g) || []).length;
            braceLevel -= (line.match(/}/g) || []).length;

            // Import statements
            const importMatch = trimmed.match(/^import\s+(.+?)\s+from\s+['"]([^'"]+)['"]|^const\s+(.+?)\s*=\s*require\(['"]([^'"]+)['"]\)/);
            if (importMatch) {
                structure.imports.push({
                    line: i + 1,
                    imported: importMatch[1] || importMatch[3],
                    from: importMatch[2] || importMatch[4],
                    statement: trimmed
                });
            }

            // Export statements
            if (trimmed.startsWith('export ')) {
                structure.exports.push({
                    line: i + 1,
                    statement: trimmed,
                    type: trimmed.includes('default') ? 'default' : 'named'
                });
            }

            // Class declarations
            const classMatch = trimmed.match(/^class\s+(\w+)(?:\s+extends\s+(\w+))?/);
            if (classMatch) {
                currentClass = {
                    name: classMatch[1],
                    extends: classMatch[2],
                    line: i + 1,
                    methods: [],
                    properties: [],
                    endLine: null
                };
                structure.classes.push(currentClass);
            }

            // Function declarations
            const funcMatch = trimmed.match(/^(?:async\s+)?(?:function\s+(\w+)|(\w+)\s*[:=]\s*(?:async\s+)?(?:function|\([^)]*\)\s*=>))/);
            if (funcMatch) {
                const funcName = funcMatch[1] || funcMatch[2];
                const func = {
                    name: funcName,
                    line: i + 1,
                    async: trimmed.includes('async'),
                    parameters: this.extractParameters(trimmed),
                    complexity: 1,
                    endLine: null
                };

                if (currentClass) {
                    currentClass.methods.push(func);
                } else {
                    structure.functions.push(func);
                }
                currentFunction = func;
            }

            // Variable declarations
            const varMatch = trimmed.match(/^(?:let|const|var)\s+(\w+)/);
            if (varMatch && !trimmed.includes('require(')) {
                structure.variables.push({
                    name: varMatch[1],
                    line: i + 1,
                    type: trimmed.startsWith('const') ? 'const' : 
                          trimmed.startsWith('let') ? 'let' : 'var',
                    scope: currentClass ? 'class' : currentFunction ? 'function' : 'global'
                });
            }

            // Control flow (for complexity calculation)
            if (currentFunction && /\b(if|for|while|switch|catch|&&|\|\|)\b/.test(trimmed)) {
                currentFunction.complexity++;
            }
        }

        return structure;
    }

    analyzePythonStructure(content) {
        const structure = {
            functions: [],
            classes: [],
            variables: [],
            imports: [],
            exports: [],
            blocks: []
        };

        const lines = content.split('\n');
        let currentClass = null;
        let currentFunction = null;
        let indentLevel = 0;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const trimmed = line.trim();
            const currentIndent = line.length - line.trimStart().length;

            // Import statements
            const importMatch = trimmed.match(/^(?:import\s+(.+)|from\s+(.+?)\s+import\s+(.+))/);
            if (importMatch) {
                structure.imports.push({
                    line: i + 1,
                    module: importMatch[2] || importMatch[1],
                    imported: importMatch[3] || importMatch[1],
                    statement: trimmed
                });
            }

            // Class definitions
            const classMatch = trimmed.match(/^class\s+(\w+)(?:\(([^)]+)\))?:/);
            if (classMatch) {
                currentClass = {
                    name: classMatch[1],
                    inherits: classMatch[2],
                    line: i + 1,
                    methods: [],
                    properties: [],
                    indent: currentIndent
                };
                structure.classes.push(currentClass);
            }

            // Function definitions
            const funcMatch = trimmed.match(/^(?:async\s+)?def\s+(\w+)\s*\(([^)]*)\):/);
            if (funcMatch) {
                const func = {
                    name: funcMatch[1],
                    line: i + 1,
                    async: trimmed.startsWith('async'),
                    parameters: funcMatch[2].split(',').map(p => p.trim()).filter(p => p),
                    complexity: 1,
                    indent: currentIndent
                };

                if (currentClass && currentIndent > currentClass.indent) {
                    currentClass.methods.push(func);
                } else {
                    structure.functions.push(func);
                    currentClass = null; // Reset if we're outside class scope
                }
                currentFunction = func;
            }

            // Variable assignments
            const varMatch = trimmed.match(/^(\w+)\s*=/);
            if (varMatch && !trimmed.includes('def ') && !trimmed.includes('class ')) {
                structure.variables.push({
                    name: varMatch[1],
                    line: i + 1,
                    scope: currentClass ? 'class' : currentFunction ? 'function' : 'global',
                    indent: currentIndent
                });
            }

            // Control flow (for complexity calculation)
            if (currentFunction && /\b(if|for|while|try|except|elif|and|or)\b/.test(trimmed)) {
                currentFunction.complexity++;
            }
        }

        return structure;
    }

    analyzeJavaStructure(content) {
        // Similar to JavaScript but with Java-specific syntax
        return {
            functions: [],
            classes: [],
            variables: [],
            imports: [],
            exports: [],
            blocks: []
        };
    }

    analyzeDependencies(content, language) {
        const dependencies = {
            internal: [], // Files within the project
            external: [], // External libraries/packages
            circular: []  // Potential circular dependencies
        };

        const lines = content.split('\n');

        for (const line of lines) {
            const trimmed = line.trim();

            switch (language) {
                case 'javascript':
                case 'typescript':
                    this.analyzeJSDependencies(trimmed, dependencies);
                    break;
                case 'python':
                    this.analyzePythonDependencies(trimmed, dependencies);
                    break;
            }
        }

        return dependencies;
    }

    analyzeJSDependencies(line, dependencies) {
        // ES6 imports
        const importMatch = line.match(/^import\s+.+?\s+from\s+['"]([^'"]+)['"]/);
        if (importMatch) {
            const module = importMatch[1];
            if (module.startsWith('./') || module.startsWith('../')) {
                dependencies.internal.push(module);
            } else {
                dependencies.external.push(module);
            }
        }

        // CommonJS requires
        const requireMatch = line.match(/require\(['"]([^'"]+)['"]\)/);
        if (requireMatch) {
            const module = requireMatch[1];
            if (module.startsWith('./') || module.startsWith('../')) {
                dependencies.internal.push(module);
            } else {
                dependencies.external.push(module);
            }
        }
    }

    analyzePythonDependencies(line, dependencies) {
        // Python imports
        const importMatch = line.match(/^(?:import\s+(.+)|from\s+(.+?)\s+import)/);
        if (importMatch) {
            const module = importMatch[1] || importMatch[2];
            if (module.startsWith('.')) {
                dependencies.internal.push(module);
            } else {
                dependencies.external.push(module);
            }
        }
    }

    calculateComplexity(content, language) {
        const complexity = {
            cyclomatic: 1, // Base complexity
            cognitive: 0,
            nesting: 0,
            functions: []
        };

        const lines = content.split('\n');
        let currentFunction = null;
        let nestingLevel = 0;
        let maxNesting = 0;

        for (const line of lines) {
            const trimmed = line.trim();

            // Track nesting level
            if (trimmed.includes('{') || trimmed.endsWith(':')) {
                nestingLevel++;
                maxNesting = Math.max(maxNesting, nestingLevel);
            }
            if (trimmed.includes('}')) {
                nestingLevel--;
            }

            // Count complexity-increasing constructs
            const complexityPatterns = [
                /\bif\b/, /\belse\b/, /\belif\b/,
                /\bfor\b/, /\bwhile\b/,
                /\bswitch\b/, /\bcase\b/,
                /\btry\b/, /\bcatch\b/, /\bexcept\b/,
                /\b&&\b/, /\b\|\|\b/, /\band\b/, /\bor\b/,
                /\?\s*.*\s*:/  // Ternary operator
            ];

            for (const pattern of complexityPatterns) {
                if (pattern.test(trimmed)) {
                    complexity.cyclomatic++;
                    complexity.cognitive += nestingLevel; // Cognitive complexity considers nesting
                }
            }
        }

        complexity.nesting = maxNesting;
        return complexity;
    }

    detectIssues(content, language) {
        const issues = [];

        const lines = content.split('\n');

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const trimmed = line.trim();

            // Long lines
            if (line.length > 120) {
                issues.push({
                    type: 'style',
                    severity: 'warning',
                    line: i + 1,
                    message: 'Line too long (>120 characters)',
                    suggestion: 'Consider breaking this line into multiple lines'
                });
            }

            // TODO comments
            if (trimmed.includes('TODO') || trimmed.includes('FIXME')) {
                issues.push({
                    type: 'todo',
                    severity: 'info',
                    line: i + 1,
                    message: 'TODO/FIXME comment found',
                    suggestion: 'Consider addressing this TODO item'
                });
            }

            // Language-specific issues
            switch (language) {
                case 'javascript':
                case 'typescript':
                    this.detectJSIssues(trimmed, i + 1, issues);
                    break;
                case 'python':
                    this.detectPythonIssues(trimmed, i + 1, issues);
                    break;
            }
        }

        return issues;
    }

    detectJSIssues(line, lineNumber, issues) {
        // console.log statements
        if (line.includes('console.log')) {
            issues.push({
                type: 'debug',
                severity: 'warning',
                line: lineNumber,
                message: 'console.log statement found',
                suggestion: 'Remove debug statements before production'
            });
        }

        // == instead of ===
        if (line.includes('==') && !line.includes('===')) {
            issues.push({
                type: 'style',
                severity: 'warning',
                line: lineNumber,
                message: 'Use === instead of ==',
                suggestion: 'Use strict equality (===) for better type safety'
            });
        }

        // var usage
        if (line.match(/\bvar\s+/)) {
            issues.push({
                type: 'style',
                severity: 'info',
                line: lineNumber,
                message: 'Consider using let or const instead of var',
                suggestion: 'Use let for mutable variables, const for immutable'
            });
        }
    }

    detectPythonIssues(line, lineNumber, issues) {
        // print statements
        if (line.includes('print(')) {
            issues.push({
                type: 'debug',
                severity: 'warning',
                line: lineNumber,
                message: 'print statement found',
                suggestion: 'Consider using logging instead of print'
            });
        }

        // Long function definitions
        if (line.startsWith('def ') && line.length > 80) {
            issues.push({
                type: 'style',
                severity: 'warning',
                line: lineNumber,
                message: 'Function definition too long',
                suggestion: 'Consider breaking parameters into multiple lines'
            });
        }
    }

    generateSuggestions(analysis) {
        const suggestions = [];

        // Complexity suggestions
        if (analysis.complexity.cyclomatic > 10) {
            suggestions.push({
                type: 'refactor',
                priority: 'high',
                message: 'High cyclomatic complexity detected',
                suggestion: 'Consider breaking this into smaller functions',
                details: `Cyclomatic complexity: ${analysis.complexity.cyclomatic}`
            });
        }

        // File size suggestions
        if (analysis.metrics.totalLines > 500) {
            suggestions.push({
                type: 'refactor',
                priority: 'medium',
                message: 'Large file detected',
                suggestion: 'Consider splitting this file into smaller modules',
                details: `Total lines: ${analysis.metrics.totalLines}`
            });
        }

        // Function count suggestions
        if (analysis.structure.functions.length > 20) {
            suggestions.push({
                type: 'refactor',
                priority: 'medium',
                message: 'Many functions in one file',
                suggestion: 'Consider organizing functions into classes or modules',
                details: `Function count: ${analysis.structure.functions.length}`
            });
        }

        // Comment ratio suggestions
        const commentRatio = analysis.metrics.commentLines / analysis.metrics.codeLines;
        if (commentRatio < 0.1) {
            suggestions.push({
                type: 'documentation',
                priority: 'low',
                message: 'Low comment ratio',
                suggestion: 'Consider adding more comments to explain complex logic',
                details: `Comment ratio: ${(commentRatio * 100).toFixed(1)}%`
            });
        }

        return suggestions;
    }

    extractParameters(functionDeclaration) {
        const paramMatch = functionDeclaration.match(/\(([^)]*)\)/);
        if (paramMatch) {
            return paramMatch[1]
                .split(',')
                .map(param => param.trim())
                .filter(param => param.length > 0);
        }
        return [];
    }

    detectLanguage(extension) {
        const languageMap = {
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.ts': 'typescript',
            '.tsx': 'typescript',
            '.py': 'python',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.h': 'c',
            '.cs': 'csharp',
            '.php': 'php',
            '.rb': 'ruby',
            '.go': 'go'
        };
        
        return languageMap[extension] || 'text';
    }

    getContentHash(content) {
        // Simple hash function for caching
        let hash = 0;
        for (let i = 0; i < content.length; i++) {
            const char = content.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString();
    }

    clearCache() {
        this.analysisCache.clear();
    }
}

module.exports = CodeAnalyzer;
