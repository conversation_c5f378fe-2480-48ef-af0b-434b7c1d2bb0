const fs = require('fs').promises;
const path = require('path');
const vscode = require('vscode');
const { spawn } = require('child_process');

class ProjectIndexer {
    constructor(context) {
        this.context = context;
        this.indexData = new Map();
        this.dependencyGraph = new Map();
        this.fileMetadata = new Map();
        this.pythonProcess = null;
        this.isIndexing = false;
        
        // Supported file extensions
        this.supportedExtensions = new Set([
            '.js', '.jsx', '.ts', '.tsx',
            '.py', '.java', '.cpp', '.c', '.h',
            '.cs', '.php', '.rb', '.go',
            '.html', '.css', '.scss', '.sass',
            '.json', '.xml', '.yaml', '.yml',
            '.md', '.txt', '.sql'
        ]);
    }

    async indexProject(projectPath, progress, token) {
        if (this.isIndexing) {
            throw new Error('Indexing already in progress');
        }

        this.isIndexing = true;
        
        try {
            console.log('Starting project indexing:', projectPath);
            
            // Clear existing index
            this.indexData.clear();
            this.dependencyGraph.clear();
            this.fileMetadata.clear();

            // Get all files to index
            const files = await this.getAllFiles(projectPath);
            const totalFiles = files.length;
            
            progress.report({ increment: 0, message: `Found ${totalFiles} files` });

            // Index files in batches
            const batchSize = 10;
            for (let i = 0; i < files.length; i += batchSize) {
                if (token.isCancellationRequested) {
                    throw new Error('Indexing cancelled');
                }

                const batch = files.slice(i, i + batchSize);
                await Promise.all(batch.map(file => this.indexFile(file)));
                
                const progressPercent = Math.floor(((i + batch.length) / totalFiles) * 100);
                progress.report({ 
                    increment: (batch.length / totalFiles) * 100,
                    message: `Indexed ${i + batch.length}/${totalFiles} files (${progressPercent}%)` 
                });
            }

            // Build dependency graph
            progress.report({ increment: 0, message: 'Building dependency graph...' });
            await this.buildDependencyGraph();

            // Start Python indexing service
            await this.startPythonIndexer(projectPath);

            console.log('Project indexing completed');
            
        } finally {
            this.isIndexing = false;
        }
    }

    async getAllFiles(dirPath) {
        const files = [];
        
        async function traverse(currentPath) {
            try {
                const entries = await fs.readdir(currentPath, { withFileTypes: true });
                
                for (const entry of entries) {
                    const fullPath = path.join(currentPath, entry.name);
                    
                    if (entry.isDirectory()) {
                        // Skip certain directories
                        if (!shouldIgnoreDirectory(entry.name)) {
                            await traverse(fullPath);
                        }
                    } else if (entry.isFile()) {
                        const ext = path.extname(entry.name);
                        if (this.supportedExtensions.has(ext)) {
                            files.push(fullPath);
                        }
                    }
                }
            } catch (error) {
                console.warn('Error reading directory:', currentPath, error.message);
            }
        }

        function shouldIgnoreDirectory(dirName) {
            const ignoreDirs = [
                'node_modules', '.git', '.vscode', 'dist', 'build',
                '__pycache__', '.pytest_cache', 'venv', 'env',
                'target', 'bin', 'obj', '.idea'
            ];
            return ignoreDirs.includes(dirName) || dirName.startsWith('.');
        }

        await traverse(dirPath);
        return files;
    }

    async indexFile(filePath) {
        try {
            const stats = await fs.stat(filePath);
            const maxSize = vscode.workspace.getConfiguration('augura').get('maxFileSize', 1048576);
            
            if (stats.size > maxSize) {
                console.warn('File too large, skipping:', filePath);
                return;
            }

            const content = await fs.readFile(filePath, 'utf8');
            const ext = path.extname(filePath);
            const relativePath = this.getRelativePath(filePath);

            // Extract metadata
            const metadata = {
                path: filePath,
                relativePath: relativePath,
                extension: ext,
                size: stats.size,
                lastModified: stats.mtime,
                lineCount: content.split('\n').length,
                language: this.detectLanguage(ext)
            };

            // Parse file content based on language
            const parseResult = await this.parseFileContent(content, metadata.language);
            
            // Store in index
            this.indexData.set(filePath, {
                metadata,
                content,
                parsed: parseResult,
                dependencies: parseResult.dependencies || [],
                exports: parseResult.exports || [],
                imports: parseResult.imports || [],
                functions: parseResult.functions || [],
                classes: parseResult.classes || [],
                variables: parseResult.variables || []
            });

            this.fileMetadata.set(filePath, metadata);

            console.log('Indexed file:', relativePath);

        } catch (error) {
            console.error('Error indexing file:', filePath, error.message);
        }
    }

    async parseFileContent(content, language) {
        const result = {
            dependencies: [],
            exports: [],
            imports: [],
            functions: [],
            classes: [],
            variables: []
        };

        try {
            switch (language) {
                case 'javascript':
                case 'typescript':
                    return this.parseJavaScript(content);
                case 'python':
                    return this.parsePython(content);
                case 'java':
                    return this.parseJava(content);
                case 'cpp':
                case 'c':
                    return this.parseC(content);
                default:
                    return this.parseGeneric(content);
            }
        } catch (error) {
            console.error('Error parsing file content:', error.message);
            return result;
        }
    }

    parseJavaScript(content) {
        const result = {
            dependencies: [],
            exports: [],
            imports: [],
            functions: [],
            classes: [],
            variables: []
        };

        const lines = content.split('\n');
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            
            // Import statements
            if (line.startsWith('import ') || line.startsWith('const ') && line.includes('require(')) {
                const importMatch = line.match(/from ['"]([^'"]+)['"]|require\(['"]([^'"]+)['"]\)/);
                if (importMatch) {
                    const moduleName = importMatch[1] || importMatch[2];
                    result.imports.push({
                        module: moduleName,
                        line: i + 1,
                        statement: line
                    });
                    result.dependencies.push(moduleName);
                }
            }
            
            // Export statements
            if (line.startsWith('export ') || line.includes('module.exports')) {
                result.exports.push({
                    line: i + 1,
                    statement: line
                });
            }
            
            // Function declarations
            const funcMatch = line.match(/(?:function\s+(\w+)|(\w+)\s*[:=]\s*(?:function|\([^)]*\)\s*=>))/);
            if (funcMatch) {
                const funcName = funcMatch[1] || funcMatch[2];
                result.functions.push({
                    name: funcName,
                    line: i + 1,
                    declaration: line
                });
            }
            
            // Class declarations
            const classMatch = line.match(/class\s+(\w+)/);
            if (classMatch) {
                result.classes.push({
                    name: classMatch[1],
                    line: i + 1,
                    declaration: line
                });
            }
            
            // Variable declarations
            const varMatch = line.match(/(?:let|const|var)\s+(\w+)/);
            if (varMatch) {
                result.variables.push({
                    name: varMatch[1],
                    line: i + 1,
                    declaration: line
                });
            }
        }

        return result;
    }

    parsePython(content) {
        const result = {
            dependencies: [],
            exports: [],
            imports: [],
            functions: [],
            classes: [],
            variables: []
        };

        const lines = content.split('\n');
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            
            // Import statements
            if (line.startsWith('import ') || line.startsWith('from ')) {
                const importMatch = line.match(/(?:import\s+(\w+)|from\s+(\w+)\s+import)/);
                if (importMatch) {
                    const moduleName = importMatch[1] || importMatch[2];
                    result.imports.push({
                        module: moduleName,
                        line: i + 1,
                        statement: line
                    });
                    result.dependencies.push(moduleName);
                }
            }
            
            // Function definitions
            const funcMatch = line.match(/def\s+(\w+)\s*\(/);
            if (funcMatch) {
                result.functions.push({
                    name: funcMatch[1],
                    line: i + 1,
                    declaration: line
                });
            }
            
            // Class definitions
            const classMatch = line.match(/class\s+(\w+)/);
            if (classMatch) {
                result.classes.push({
                    name: classMatch[1],
                    line: i + 1,
                    declaration: line
                });
            }
            
            // Variable assignments
            const varMatch = line.match(/^(\w+)\s*=/);
            if (varMatch && !line.includes('def ') && !line.includes('class ')) {
                result.variables.push({
                    name: varMatch[1],
                    line: i + 1,
                    declaration: line
                });
            }
        }

        return result;
    }

    parseJava(content) {
        // Similar parsing logic for Java
        return this.parseGeneric(content);
    }

    parseC(content) {
        // Similar parsing logic for C/C++
        return this.parseGeneric(content);
    }

    parseGeneric(content) {
        // Basic parsing for unsupported languages
        return {
            dependencies: [],
            exports: [],
            imports: [],
            functions: [],
            classes: [],
            variables: []
        };
    }

    async buildDependencyGraph() {
        this.dependencyGraph.clear();
        
        for (const [filePath, fileData] of this.indexData) {
            const dependencies = new Set();
            
            // Process imports/dependencies
            for (const dep of fileData.dependencies) {
                const resolvedPath = this.resolveDependency(filePath, dep);
                if (resolvedPath && this.indexData.has(resolvedPath)) {
                    dependencies.add(resolvedPath);
                }
            }
            
            this.dependencyGraph.set(filePath, Array.from(dependencies));
        }
    }

    resolveDependency(fromFile, dependency) {
        // Handle relative imports
        if (dependency.startsWith('./') || dependency.startsWith('../')) {
            const fromDir = path.dirname(fromFile);
            const resolvedPath = path.resolve(fromDir, dependency);
            
            // Try different extensions
            for (const ext of this.supportedExtensions) {
                const fullPath = resolvedPath + ext;
                if (this.indexData.has(fullPath)) {
                    return fullPath;
                }
            }
            
            // Try index files
            const indexPath = path.join(resolvedPath, 'index.js');
            if (this.indexData.has(indexPath)) {
                return indexPath;
            }
        }
        
        return null;
    }

    async startPythonIndexer(projectPath) {
        try {
            const pythonScript = path.join(__dirname, '../../agent_core/indexer.py');
            
            this.pythonProcess = spawn('python', [pythonScript, projectPath], {
                stdio: ['pipe', 'pipe', 'pipe']
            });

            this.pythonProcess.stdout.on('data', (data) => {
                console.log('Python indexer:', data.toString());
            });

            this.pythonProcess.stderr.on('data', (data) => {
                console.error('Python indexer error:', data.toString());
            });

        } catch (error) {
            console.error('Failed to start Python indexer:', error.message);
        }
    }

    detectLanguage(extension) {
        const languageMap = {
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.ts': 'typescript',
            '.tsx': 'typescript',
            '.py': 'python',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.h': 'c',
            '.cs': 'csharp',
            '.php': 'php',
            '.rb': 'ruby',
            '.go': 'go',
            '.html': 'html',
            '.css': 'css',
            '.scss': 'scss',
            '.sass': 'sass'
        };
        
        return languageMap[extension] || 'text';
    }

    getRelativePath(filePath) {
        if (vscode.workspace.workspaceFolders) {
            return vscode.workspace.asRelativePath(filePath);
        }
        return filePath;
    }

    async removeFile(filePath) {
        this.indexData.delete(filePath);
        this.fileMetadata.delete(filePath);
        this.dependencyGraph.delete(filePath);
        
        // Remove from other files' dependencies
        for (const [file, deps] of this.dependencyGraph) {
            const index = deps.indexOf(filePath);
            if (index > -1) {
                deps.splice(index, 1);
            }
        }
    }

    // Query methods
    searchFiles(query) {
        const results = [];
        const queryLower = query.toLowerCase();
        
        for (const [filePath, fileData] of this.indexData) {
            const score = this.calculateRelevanceScore(fileData, queryLower);
            if (score > 0) {
                results.push({
                    filePath,
                    score,
                    metadata: fileData.metadata,
                    matches: this.findMatches(fileData, queryLower)
                });
            }
        }
        
        return results.sort((a, b) => b.score - a.score);
    }

    calculateRelevanceScore(fileData, query) {
        let score = 0;
        
        // Check filename
        if (fileData.metadata.relativePath.toLowerCase().includes(query)) {
            score += 10;
        }
        
        // Check functions
        for (const func of fileData.functions) {
            if (func.name.toLowerCase().includes(query)) {
                score += 5;
            }
        }
        
        // Check classes
        for (const cls of fileData.classes) {
            if (cls.name.toLowerCase().includes(query)) {
                score += 5;
            }
        }
        
        // Check content
        if (fileData.content.toLowerCase().includes(query)) {
            score += 1;
        }
        
        return score;
    }

    findMatches(fileData, query) {
        const matches = [];
        
        // Find in functions
        fileData.functions.forEach(func => {
            if (func.name.toLowerCase().includes(query)) {
                matches.push({
                    type: 'function',
                    name: func.name,
                    line: func.line
                });
            }
        });
        
        // Find in classes
        fileData.classes.forEach(cls => {
            if (cls.name.toLowerCase().includes(query)) {
                matches.push({
                    type: 'class',
                    name: cls.name,
                    line: cls.line
                });
            }
        });
        
        return matches;
    }

    getFileData(filePath) {
        return this.indexData.get(filePath);
    }

    getDependencies(filePath) {
        return this.dependencyGraph.get(filePath) || [];
    }

    getDependents(filePath) {
        const dependents = [];
        for (const [file, deps] of this.dependencyGraph) {
            if (deps.includes(filePath)) {
                dependents.push(file);
            }
        }
        return dependents;
    }

    dispose() {
        if (this.pythonProcess) {
            this.pythonProcess.kill();
        }
    }
}

module.exports = ProjectIndexer;
