const vscode = require('vscode');
const fs = require('fs').promises;
const path = require('path');

class MemoryManager {
    constructor(context) {
        this.context = context;
        this.memoryPath = path.join(context.globalStorageUri.fsPath, 'augura_memory');
        this.conversationMemory = new Map();
        this.projectMemory = new Map();
        this.codebaseMemory = new Map();
        this.userPreferences = new Map();
        this.sessionMemory = new Map();
        
        // Memory categories
        this.memoryTypes = {
            CONVERSATION: 'conversation',
            PROJECT: 'project',
            CODEBASE: 'codebase',
            USER_PREFERENCES: 'user_preferences',
            SESSION: 'session',
            FILE_CHANGES: 'file_changes',
            LEARNING: 'learning'
        };
        
        this.isInitialized = false;
    }

    async initialize() {
        try {
            // Ensure memory directory exists
            await this.ensureMemoryDirectory();
            
            // Load existing memories
            await this.loadAllMemories();
            
            // Initialize session
            this.initializeSession();
            
            this.isInitialized = true;
            console.log('Memory Manager initialized successfully');
            
        } catch (error) {
            console.error('Error initializing Memory Manager:', error);
            throw error;
        }
    }

    async ensureMemoryDirectory() {
        try {
            await fs.access(this.memoryPath);
        } catch (error) {
            // Directory doesn't exist, create it
            await fs.mkdir(this.memoryPath, { recursive: true });
            console.log('Created memory directory:', this.memoryPath);
        }
    }

    async loadAllMemories() {
        const memoryFiles = [
            'conversations.json',
            'projects.json',
            'codebase.json',
            'preferences.json',
            'file_changes.json',
            'learning.json'
        ];

        for (const file of memoryFiles) {
            try {
                const filePath = path.join(this.memoryPath, file);
                const content = await fs.readFile(filePath, 'utf8');
                const data = JSON.parse(content);
                
                switch (file) {
                    case 'conversations.json':
                        this.conversationMemory = new Map(Object.entries(data));
                        break;
                    case 'projects.json':
                        this.projectMemory = new Map(Object.entries(data));
                        break;
                    case 'codebase.json':
                        this.codebaseMemory = new Map(Object.entries(data));
                        break;
                    case 'preferences.json':
                        this.userPreferences = new Map(Object.entries(data));
                        break;
                }
                
                console.log(`Loaded ${file}:`, Object.keys(data).length, 'entries');
                
            } catch (error) {
                // File doesn't exist or is corrupted, start fresh
                console.log(`No existing ${file} found, starting fresh`);
            }
        }
    }

    initializeSession() {
        const sessionId = this.generateSessionId();
        const workspacePath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || 'no-workspace';
        
        this.sessionMemory.set('current', {
            sessionId,
            startTime: new Date().toISOString(),
            workspacePath,
            interactions: [],
            fileChanges: [],
            codeModifications: [],
            learnings: []
        });
    }

    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // Conversation Memory Management
    async storeConversation(conversationId, messages, metadata = {}) {
        const conversation = {
            id: conversationId,
            messages,
            metadata: {
                ...metadata,
                createdAt: new Date().toISOString(),
                lastUpdated: new Date().toISOString(),
                messageCount: messages.length
            }
        };

        this.conversationMemory.set(conversationId, conversation);
        await this.saveMemoryType(this.memoryTypes.CONVERSATION);
        
        // Update session memory
        const session = this.sessionMemory.get('current');
        if (session) {
            session.interactions.push({
                type: 'conversation',
                conversationId,
                timestamp: new Date().toISOString(),
                messageCount: messages.length
            });
        }
    }

    async getConversation(conversationId) {
        return this.conversationMemory.get(conversationId);
    }

    async getRecentConversations(limit = 10) {
        const conversations = Array.from(this.conversationMemory.values())
            .sort((a, b) => new Date(b.metadata.lastUpdated) - new Date(a.metadata.lastUpdated))
            .slice(0, limit);
        
        return conversations;
    }

    async searchConversations(query) {
        const results = [];
        const queryLower = query.toLowerCase();
        
        for (const conversation of this.conversationMemory.values()) {
            let relevanceScore = 0;
            const matches = [];
            
            for (const message of conversation.messages) {
                if (message.content && message.content.toLowerCase().includes(queryLower)) {
                    relevanceScore += 1;
                    matches.push({
                        role: message.role,
                        content: message.content.substring(0, 200) + '...',
                        timestamp: message.timestamp
                    });
                }
            }
            
            if (relevanceScore > 0) {
                results.push({
                    conversation,
                    relevanceScore,
                    matches
                });
            }
        }
        
        return results.sort((a, b) => b.relevanceScore - a.relevanceScore);
    }

    // Project Memory Management
    async storeProjectInfo(projectPath, projectInfo) {
        const projectKey = this.getProjectKey(projectPath);
        const existingInfo = this.projectMemory.get(projectKey) || {};
        
        const updatedInfo = {
            ...existingInfo,
            ...projectInfo,
            path: projectPath,
            lastUpdated: new Date().toISOString(),
            accessCount: (existingInfo.accessCount || 0) + 1
        };
        
        this.projectMemory.set(projectKey, updatedInfo);
        await this.saveMemoryType(this.memoryTypes.PROJECT);
    }

    async getProjectInfo(projectPath) {
        const projectKey = this.getProjectKey(projectPath);
        return this.projectMemory.get(projectKey);
    }

    getProjectKey(projectPath) {
        // Create a consistent key from project path
        return Buffer.from(projectPath).toString('base64').replace(/[/+=]/g, '_');
    }

    // Codebase Memory Management
    async storeCodebaseKnowledge(filePath, knowledge) {
        const fileKey = this.getFileKey(filePath);
        const existing = this.codebaseMemory.get(fileKey) || {};
        
        const updated = {
            ...existing,
            ...knowledge,
            filePath,
            lastAnalyzed: new Date().toISOString(),
            analysisCount: (existing.analysisCount || 0) + 1
        };
        
        this.codebaseMemory.set(fileKey, updated);
        await this.saveMemoryType(this.memoryTypes.CODEBASE);
    }

    async getCodebaseKnowledge(filePath) {
        const fileKey = this.getFileKey(filePath);
        return this.codebaseMemory.get(fileKey);
    }

    async searchCodebaseKnowledge(query) {
        const results = [];
        const queryLower = query.toLowerCase();
        
        for (const [fileKey, knowledge] of this.codebaseMemory) {
            let relevanceScore = 0;
            
            // Check file path
            if (knowledge.filePath && knowledge.filePath.toLowerCase().includes(queryLower)) {
                relevanceScore += 5;
            }
            
            // Check functions
            if (knowledge.functions) {
                for (const func of knowledge.functions) {
                    if (func.name && func.name.toLowerCase().includes(queryLower)) {
                        relevanceScore += 3;
                    }
                }
            }
            
            // Check classes
            if (knowledge.classes) {
                for (const cls of knowledge.classes) {
                    if (cls.name && cls.name.toLowerCase().includes(queryLower)) {
                        relevanceScore += 3;
                    }
                }
            }
            
            // Check description
            if (knowledge.description && knowledge.description.toLowerCase().includes(queryLower)) {
                relevanceScore += 2;
            }
            
            if (relevanceScore > 0) {
                results.push({
                    filePath: knowledge.filePath,
                    knowledge,
                    relevanceScore
                });
            }
        }
        
        return results.sort((a, b) => b.relevanceScore - a.relevanceScore);
    }

    getFileKey(filePath) {
        // Create a consistent key from file path
        return Buffer.from(filePath).toString('base64').replace(/[/+=]/g, '_');
    }

    // User Preferences Management
    async storeUserPreference(key, value) {
        this.userPreferences.set(key, {
            value,
            updatedAt: new Date().toISOString()
        });
        await this.saveMemoryType(this.memoryTypes.USER_PREFERENCES);
    }

    async getUserPreference(key, defaultValue = null) {
        const pref = this.userPreferences.get(key);
        return pref ? pref.value : defaultValue;
    }

    async getAllUserPreferences() {
        const prefs = {};
        for (const [key, data] of this.userPreferences) {
            prefs[key] = data.value;
        }
        return prefs;
    }

    // File Changes Tracking
    async updateFileChange(changeType, filePath, metadata = {}) {
        const changeId = `${changeType}_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
        const change = {
            id: changeId,
            type: changeType, // 'create', 'modify', 'delete'
            filePath,
            timestamp: new Date().toISOString(),
            metadata
        };
        
        // Store in session memory
        const session = this.sessionMemory.get('current');
        if (session) {
            session.fileChanges.push(change);
            
            // Keep only last 100 changes in session
            if (session.fileChanges.length > 100) {
                session.fileChanges = session.fileChanges.slice(-100);
            }
        }
        
        // Store in persistent memory
        await this.appendToMemoryFile('file_changes.json', change);
    }

    async getRecentFileChanges(limit = 50) {
        const session = this.sessionMemory.get('current');
        if (session) {
            return session.fileChanges.slice(-limit);
        }
        return [];
    }

    // Learning and Adaptation
    async recordLearning(category, learning) {
        const learningId = `learning_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
        const record = {
            id: learningId,
            category, // 'user_pattern', 'code_style', 'preference', 'error_pattern'
            learning,
            timestamp: new Date().toISOString(),
            confidence: learning.confidence || 0.5
        };
        
        // Store in session memory
        const session = this.sessionMemory.get('current');
        if (session) {
            session.learnings.push(record);
        }
        
        // Store in persistent memory
        await this.appendToMemoryFile('learning.json', record);
    }

    async getLearnings(category = null, limit = 100) {
        const session = this.sessionMemory.get('current');
        if (!session) return [];
        
        let learnings = session.learnings;
        
        if (category) {
            learnings = learnings.filter(l => l.category === category);
        }
        
        return learnings.slice(-limit);
    }

    // Context Building
    async buildContextForQuery(query, options = {}) {
        const context = {
            query,
            timestamp: new Date().toISOString(),
            relevantConversations: [],
            relevantCode: [],
            userPreferences: {},
            recentChanges: [],
            learnings: []
        };
        
        try {
            // Get relevant conversations
            if (options.includeConversations !== false) {
                const conversations = await this.searchConversations(query);
                context.relevantConversations = conversations.slice(0, 5);
            }
            
            // Get relevant codebase knowledge
            if (options.includeCodebase !== false) {
                const codeKnowledge = await this.searchCodebaseKnowledge(query);
                context.relevantCode = codeKnowledge.slice(0, 10);
            }
            
            // Get user preferences
            if (options.includePreferences !== false) {
                context.userPreferences = await this.getAllUserPreferences();
            }
            
            // Get recent file changes
            if (options.includeRecentChanges !== false) {
                context.recentChanges = await this.getRecentFileChanges(20);
            }
            
            // Get relevant learnings
            if (options.includeLearnings !== false) {
                context.learnings = await this.getLearnings(null, 20);
            }
            
        } catch (error) {
            console.error('Error building context:', error);
        }
        
        return context;
    }

    // Memory Persistence
    async saveMemoryType(memoryType) {
        try {
            let data = {};
            let filename = '';
            
            switch (memoryType) {
                case this.memoryTypes.CONVERSATION:
                    data = Object.fromEntries(this.conversationMemory);
                    filename = 'conversations.json';
                    break;
                case this.memoryTypes.PROJECT:
                    data = Object.fromEntries(this.projectMemory);
                    filename = 'projects.json';
                    break;
                case this.memoryTypes.CODEBASE:
                    data = Object.fromEntries(this.codebaseMemory);
                    filename = 'codebase.json';
                    break;
                case this.memoryTypes.USER_PREFERENCES:
                    data = Object.fromEntries(this.userPreferences);
                    filename = 'preferences.json';
                    break;
                default:
                    throw new Error(`Unknown memory type: ${memoryType}`);
            }
            
            const filePath = path.join(this.memoryPath, filename);
            await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');
            
        } catch (error) {
            console.error(`Error saving memory type ${memoryType}:`, error);
        }
    }

    async appendToMemoryFile(filename, data) {
        try {
            const filePath = path.join(this.memoryPath, filename);
            let existingData = [];
            
            try {
                const content = await fs.readFile(filePath, 'utf8');
                existingData = JSON.parse(content);
            } catch (error) {
                // File doesn't exist or is empty
            }
            
            if (!Array.isArray(existingData)) {
                existingData = [];
            }
            
            existingData.push(data);
            
            // Keep only last 1000 entries to prevent file from growing too large
            if (existingData.length > 1000) {
                existingData = existingData.slice(-1000);
            }
            
            await fs.writeFile(filePath, JSON.stringify(existingData, null, 2), 'utf8');
            
        } catch (error) {
            console.error(`Error appending to memory file ${filename}:`, error);
        }
    }

    async saveAllMemories() {
        const savePromises = [
            this.saveMemoryType(this.memoryTypes.CONVERSATION),
            this.saveMemoryType(this.memoryTypes.PROJECT),
            this.saveMemoryType(this.memoryTypes.CODEBASE),
            this.saveMemoryType(this.memoryTypes.USER_PREFERENCES)
        ];
        
        await Promise.all(savePromises);
    }

    // Memory Statistics
    getMemoryStats() {
        return {
            conversations: this.conversationMemory.size,
            projects: this.projectMemory.size,
            codebaseEntries: this.codebaseMemory.size,
            userPreferences: this.userPreferences.size,
            sessionActive: this.sessionMemory.has('current'),
            memoryPath: this.memoryPath,
            isInitialized: this.isInitialized
        };
    }

    // Memory Cleanup
    async cleanupOldMemories(daysToKeep = 30) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
        
        let cleanedCount = 0;
        
        // Clean old conversations
        for (const [id, conversation] of this.conversationMemory) {
            const lastUpdated = new Date(conversation.metadata.lastUpdated);
            if (lastUpdated < cutoffDate) {
                this.conversationMemory.delete(id);
                cleanedCount++;
            }
        }
        
        // Clean old codebase entries
        for (const [key, knowledge] of this.codebaseMemory) {
            const lastAnalyzed = new Date(knowledge.lastAnalyzed);
            if (lastAnalyzed < cutoffDate) {
                this.codebaseMemory.delete(key);
                cleanedCount++;
            }
        }
        
        if (cleanedCount > 0) {
            await this.saveAllMemories();
            console.log(`Cleaned up ${cleanedCount} old memory entries`);
        }
        
        return cleanedCount;
    }

    // Export/Import
    async exportMemories() {
        const exportData = {
            conversations: Object.fromEntries(this.conversationMemory),
            projects: Object.fromEntries(this.projectMemory),
            codebase: Object.fromEntries(this.codebaseMemory),
            preferences: Object.fromEntries(this.userPreferences),
            exportedAt: new Date().toISOString()
        };
        
        return exportData;
    }

    async importMemories(importData) {
        if (importData.conversations) {
            this.conversationMemory = new Map(Object.entries(importData.conversations));
        }
        if (importData.projects) {
            this.projectMemory = new Map(Object.entries(importData.projects));
        }
        if (importData.codebase) {
            this.codebaseMemory = new Map(Object.entries(importData.codebase));
        }
        if (importData.preferences) {
            this.userPreferences = new Map(Object.entries(importData.preferences));
        }
        
        await this.saveAllMemories();
    }

    dispose() {
        // Save all memories before disposing
        this.saveAllMemories().catch(error => {
            console.error('Error saving memories during disposal:', error);
        });
    }
}

module.exports = MemoryManager;
