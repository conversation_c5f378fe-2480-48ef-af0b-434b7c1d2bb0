const vscode = require('vscode');
const path = require('path');
const fs = require('fs');

class ChatProvider {
    constructor(context, aiAgent) {
        this.context = context;
        this.aiAgent = aiAgent;
        this.webview = null;
        this.isStreaming = false;
        this.currentMessageId = null;
    }

    resolveWebviewView(webviewView, context, token) {
        this.webview = webviewView.webview;
        
        this.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                vscode.Uri.joinPath(this.context.extensionUri, 'template'),
                vscode.Uri.joinPath(this.context.extensionUri, 'src', 'webview')
            ]
        };

        this.webview.html = this.getWebviewContent();
        this.setupMessageHandling();
    }

    getWebviewContent() {
        // Use the existing chat.html but with enhanced functionality
        const templatePath = path.join(this.context.extensionPath, 'template', 'chat.html');
        let html = fs.readFileSync(templatePath, 'utf8');
        
        // Replace relative paths with webview URIs
        const stylesPath = this.webview.asWebviewUri(
            vscode.Uri.joinPath(this.context.extensionUri, 'template', 'styles')
        );
        const scriptsPath = this.webview.asWebviewUri(
            vscode.Uri.joinPath(this.context.extensionUri, 'template', 'scripts')
        );
        
        html = html.replace(/styles\//g, stylesPath.toString() + '/');
        html = html.replace(/scripts\//g, scriptsPath.toString() + '/');
        
        // Add enhanced functionality script
        const enhancedScript = `
        <script>
            // Enhanced functionality for AI agent
            let isAgentMode = true;
            let currentTask = null;
            let projectContext = null;
            
            // Override the original sendMessage function
            const originalSendMessage = window.sendMessage;
            window.sendMessage = async function() {
                const messageInput = document.getElementById('messageInput');
                const message = messageInput.value.trim();
                
                if (!message) return;
                
                // Add user message to chat
                addMessage('user', message);
                messageInput.value = '';
                updateSendButton();
                
                // Show typing indicator
                showTyping(true);
                
                // Send to extension
                vscode.postMessage({
                    type: 'sendMessage',
                    message: message,
                    timestamp: new Date().toISOString()
                });
            };
            
            // Enhanced message handling
            window.addEventListener('message', event => {
                const message = event.data;
                
                switch (message.type) {
                    case 'streamStart':
                        startStreamingResponse(message.messageId);
                        break;
                    case 'streamChunk':
                        addStreamChunk(message.chunk);
                        break;
                    case 'streamEnd':
                        endStreamingResponse();
                        break;
                    case 'showAnalysis':
                        showAnalysisResult(message.analysis);
                        break;
                    case 'showRefactoringSuggestions':
                        showRefactoringSuggestions(message.suggestions);
                        break;
                    case 'showCodeChanges':
                        showCodeChanges(message.changes);
                        break;
                    case 'updateProjectContext':
                        updateProjectContext(message.context);
                        break;
                    case 'showError':
                        showErrorMessage(message.error);
                        break;
                }
            });
            
            function startStreamingResponse(messageId) {
                currentStreamingMessageId = messageId;
                showTyping(false);
                
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message assistant-message streaming';
                messageDiv.id = 'streaming-message-' + messageId;
                messageDiv.innerHTML = \`
                    <div class="message-header">
                        <div class="message-role">
                            <i class="codicon codicon-robot"></i>
                            <span>Augura Coder</span>
                        </div>
                        <div class="message-actions">
                            <button class="action-btn" onclick="copyMessage('\${messageId}')" title="Copy">
                                <i class="codicon codicon-copy"></i>
                            </button>
                        </div>
                    </div>
                    <div class="message-content" id="streaming-content-\${messageId}">
                        <div class="typing-indicator">
                            <span class="typing-dots">Thinking</span>
                        </div>
                    </div>
                \`;
                
                chatContainer.appendChild(messageDiv);
                scrollToBottom();
            }
            
            function addStreamChunk(chunk) {
                if (!currentStreamingMessageId) return;
                
                const contentDiv = document.getElementById('streaming-content-' + currentStreamingMessageId);
                if (!contentDiv) return;
                
                // Remove typing indicator if it exists
                const typingIndicator = contentDiv.querySelector('.typing-indicator');
                if (typingIndicator) {
                    typingIndicator.remove();
                }
                
                // Add chunk to content
                if (!contentDiv.querySelector('.streaming-text')) {
                    contentDiv.innerHTML = '<div class="streaming-text"></div>';
                }
                
                const streamingText = contentDiv.querySelector('.streaming-text');
                streamingText.textContent += chunk;
                
                scrollToBottom();
            }
            
            function endStreamingResponse() {
                if (!currentStreamingMessageId) return;
                
                const messageDiv = document.getElementById('streaming-message-' + currentStreamingMessageId);
                const contentDiv = document.getElementById('streaming-content-' + currentStreamingMessageId);
                
                if (messageDiv && contentDiv) {
                    messageDiv.classList.remove('streaming');
                    
                    const streamingText = contentDiv.querySelector('.streaming-text');
                    if (streamingText) {
                        const content = streamingText.textContent;
                        contentDiv.innerHTML = marked.parse(content);
                        
                        // Re-highlight code blocks
                        contentDiv.querySelectorAll('pre code').forEach(block => {
                            if (window.Prism) {
                                Prism.highlightElement(block);
                            }
                        });
                        
                        // Add code block actions
                        addCodeBlockActions(contentDiv);
                    }
                }
                
                currentStreamingMessageId = null;
                scrollToBottom();
            }
            
            function showAnalysisResult(analysis) {
                const messageId = 'analysis-' + Date.now();
                
                let content = \`## Code Analysis Results\\n\\n\`;
                content += \`**File:** \${analysis.filePath}\\n\`;
                content += \`**Language:** \${analysis.language}\\n\\n\`;
                
                // Metrics
                content += \`### Metrics\\n\`;
                content += \`- Total Lines: \${analysis.metrics.totalLines}\\n\`;
                content += \`- Code Lines: \${analysis.metrics.codeLines}\\n\`;
                content += \`- Comment Lines: \${analysis.metrics.commentLines}\\n\`;
                content += \`- Complexity: \${analysis.complexity.cyclomatic}\\n\\n\`;
                
                // Structure
                if (analysis.structure.functions.length > 0) {
                    content += \`### Functions (\${analysis.structure.functions.length})\\n\`;
                    analysis.structure.functions.forEach(func => {
                        content += \`- \${func.name} (line \${func.line})\\n\`;
                    });
                    content += \`\\n\`;
                }
                
                if (analysis.structure.classes.length > 0) {
                    content += \`### Classes (\${analysis.structure.classes.length})\\n\`;
                    analysis.structure.classes.forEach(cls => {
                        content += \`- \${cls.name} (line \${cls.line})\\n\`;
                    });
                    content += \`\\n\`;
                }
                
                // Issues
                if (analysis.issues.length > 0) {
                    content += \`### Issues Found\\n\`;
                    analysis.issues.forEach(issue => {
                        content += \`- **Line \${issue.line}:** \${issue.message}\\n\`;
                    });
                    content += \`\\n\`;
                }
                
                // Suggestions
                if (analysis.suggestions.length > 0) {
                    content += \`### Suggestions\\n\`;
                    analysis.suggestions.forEach(suggestion => {
                        content += \`- **\${suggestion.type}:** \${suggestion.message}\\n\`;
                    });
                }
                
                addMessage('assistant', content, messageId);
            }
            
            function showRefactoringSuggestions(suggestions) {
                const messageId = 'refactor-' + Date.now();
                
                let content = \`## Refactoring Suggestions\\n\\n\`;
                
                if (suggestions.content) {
                    content += suggestions.content;
                } else {
                    content += \`No specific suggestions available.\`;
                }
                
                addMessage('assistant', content, messageId);
            }
            
            function showCodeChanges(changes) {
                const messageId = 'changes-' + Date.now();
                
                let content = \`## Code Changes Applied\\n\\n\`;
                
                changes.forEach((change, index) => {
                    content += \`### Change \${index + 1}: \${change.type}\\n\`;
                    content += \`**File:** \${change.file}\\n\`;
                    if (change.description) {
                        content += \`**Description:** \${change.description}\\n\`;
                    }
                    content += \`\\n\`;
                });
                
                addMessage('assistant', content, messageId);
            }
            
            function updateProjectContext(context) {
                projectContext = context;
                
                // Update UI to show project info
                const welcomeMessage = document.querySelector('.welcome-message');
                if (welcomeMessage && context) {
                    const projectInfo = document.createElement('div');
                    projectInfo.className = 'project-info';
                    projectInfo.innerHTML = \`
                        <div class="project-details">
                            <strong>Project:</strong> \${context.name || 'Unknown'}
                            <br>
                            <strong>Files:</strong> \${context.totalFiles || 0}
                        </div>
                    \`;
                    welcomeMessage.appendChild(projectInfo);
                }
            }
            
            function showErrorMessage(error) {
                const messageId = 'error-' + Date.now();
                const content = \`## Error\\n\\n❌ \${error.message}\\n\\n\${error.details || ''}\`;
                addMessage('assistant', content, messageId);
            }
            
            function addCodeBlockActions(container) {
                const codeBlocks = container.querySelectorAll('pre code');
                codeBlocks.forEach(block => {
                    const pre = block.parentElement;
                    if (!pre.querySelector('.code-actions')) {
                        const actions = document.createElement('div');
                        actions.className = 'code-actions';
                        actions.innerHTML = \`
                            <button class="code-action-btn" onclick="copyCodeBlock(this)" title="Copy">
                                <i class="codicon codicon-copy"></i>
                            </button>
                            <button class="code-action-btn" onclick="applyCodeBlock(this)" title="Apply">
                                <i class="codicon codicon-check"></i>
                            </button>
                        \`;
                        pre.appendChild(actions);
                    }
                });
            }
            
            function applyCodeBlock(button) {
                const pre = button.closest('pre');
                const code = pre.querySelector('code').textContent;
                
                vscode.postMessage({
                    type: 'applyCode',
                    code: code,
                    language: pre.querySelector('code').className.replace('language-', '')
                });
            }
            
            // Enhanced message display
            function addMessage(role, content, messageId = null) {
                if (!messageId) {
                    messageId = role + '-' + Date.now();
                }
                
                const messageDiv = document.createElement('div');
                messageDiv.className = \`message \${role}-message\`;
                messageDiv.id = 'message-' + messageId;
                
                const roleIcon = role === 'user' ? 'codicon-person' : 'codicon-robot';
                const roleName = role === 'user' ? 'You' : 'Augura Coder';
                
                messageDiv.innerHTML = \`
                    <div class="message-header">
                        <div class="message-role">
                            <i class="codicon \${roleIcon}"></i>
                            <span>\${roleName}</span>
                        </div>
                        <div class="message-actions">
                            <button class="action-btn" onclick="copyMessage('\${messageId}')" title="Copy">
                                <i class="codicon codicon-copy"></i>
                            </button>
                            \${role === 'assistant' ? \`
                                <button class="action-btn" onclick="regenerateMessage('\${messageId}')" title="Regenerate">
                                    <i class="codicon codicon-refresh"></i>
                                </button>
                            \` : ''}
                        </div>
                    </div>
                    <div class="message-content">
                        \${role === 'user' ? content : marked.parse(content)}
                    </div>
                \`;
                
                chatContainer.appendChild(messageDiv);
                
                // Highlight code blocks for assistant messages
                if (role === 'assistant') {
                    messageDiv.querySelectorAll('pre code').forEach(block => {
                        if (window.Prism) {
                            Prism.highlightElement(block);
                        }
                    });
                    
                    addCodeBlockActions(messageDiv);
                }
                
                scrollToBottom();
            }
            
            function copyMessage(messageId) {
                const messageDiv = document.getElementById('message-' + messageId);
                const content = messageDiv.querySelector('.message-content').textContent;
                navigator.clipboard.writeText(content);
                
                // Show feedback
                const copyBtn = messageDiv.querySelector('.action-btn');
                const originalIcon = copyBtn.innerHTML;
                copyBtn.innerHTML = '<i class="codicon codicon-check"></i>';
                setTimeout(() => {
                    copyBtn.innerHTML = originalIcon;
                }, 1000);
            }
            
            function regenerateMessage(messageId) {
                vscode.postMessage({
                    type: 'regenerateMessage',
                    messageId: messageId
                });
            }
            
            // Initialize enhanced features
            document.addEventListener('DOMContentLoaded', function() {
                // Request project context
                vscode.postMessage({
                    type: 'requestProjectContext'
                });
            });
        </script>
        `;
        
        // Insert the enhanced script before the closing body tag
        html = html.replace('</body>', enhancedScript + '</body>');
        
        return html;
    }

    setupMessageHandling() {
        this.webview.onDidReceiveMessage(async (message) => {
            try {
                switch (message.type) {
                    case 'sendMessage':
                        await this.handleSendMessage(message);
                        break;
                    case 'applyCode':
                        await this.handleApplyCode(message);
                        break;
                    case 'regenerateMessage':
                        await this.handleRegenerateMessage(message);
                        break;
                    case 'requestProjectContext':
                        await this.handleRequestProjectContext();
                        break;
                }
            } catch (error) {
                console.error('Error handling webview message:', error);
                this.webview.postMessage({
                    type: 'showError',
                    error: {
                        message: error.message,
                        details: error.stack
                    }
                });
            }
        });
    }

    async handleSendMessage(message) {
        const messageId = 'msg-' + Date.now();
        
        try {
            // Start streaming response
            this.webview.postMessage({
                type: 'streamStart',
                messageId: messageId
            });
            
            this.isStreaming = true;
            this.currentMessageId = messageId;
            
            // Process message with AI agent using streaming
            await this.aiAgent.processMessageStream(
                message.message,
                (chunk) => {
                    if (this.isStreaming && this.currentMessageId === messageId) {
                        this.webview.postMessage({
                            type: 'streamChunk',
                            chunk: chunk
                        });
                    }
                }
            );
            
            // End streaming
            this.webview.postMessage({
                type: 'streamEnd'
            });
            
        } catch (error) {
            console.error('Error processing message:', error);
            this.webview.postMessage({
                type: 'showError',
                error: {
                    message: 'Failed to process message: ' + error.message
                }
            });
        } finally {
            this.isStreaming = false;
            this.currentMessageId = null;
        }
    }

    async handleApplyCode(message) {
        try {
            const activeEditor = vscode.window.activeTextEditor;
            if (!activeEditor) {
                vscode.window.showWarningMessage('No active editor to apply code to');
                return;
            }

            const selection = activeEditor.selection;
            const edit = new vscode.WorkspaceEdit();
            
            if (selection.isEmpty) {
                // Insert at cursor position
                edit.insert(activeEditor.document.uri, selection.start, message.code);
            } else {
                // Replace selected text
                edit.replace(activeEditor.document.uri, selection, message.code);
            }
            
            const success = await vscode.workspace.applyEdit(edit);
            
            if (success) {
                vscode.window.showInformationMessage('Code applied successfully');
            } else {
                vscode.window.showErrorMessage('Failed to apply code');
            }
            
        } catch (error) {
            console.error('Error applying code:', error);
            vscode.window.showErrorMessage('Error applying code: ' + error.message);
        }
    }

    async handleRegenerateMessage(message) {
        // Get the conversation history and regenerate the last response
        const history = this.aiAgent.getHistory();
        if (history.length >= 2) {
            const lastUserMessage = history[history.length - 2];
            if (lastUserMessage.role === 'user') {
                await this.handleSendMessage({
                    message: lastUserMessage.content,
                    type: 'sendMessage'
                });
            }
        }
    }

    async handleRequestProjectContext() {
        try {
            // Get project context from AI agent
            const context = this.aiAgent.projectContext;
            
            this.webview.postMessage({
                type: 'updateProjectContext',
                context: context
            });
            
        } catch (error) {
            console.error('Error getting project context:', error);
        }
    }

    async showAnalysis(analysis) {
        this.webview.postMessage({
            type: 'showAnalysis',
            analysis: analysis
        });
    }

    async showRefactoringSuggestions(suggestions) {
        this.webview.postMessage({
            type: 'showRefactoringSuggestions',
            suggestions: suggestions
        });
    }

    async showCodeChanges(changes) {
        this.webview.postMessage({
            type: 'showCodeChanges',
            changes: changes
        });
    }
}

module.exports = ChatProvider;
