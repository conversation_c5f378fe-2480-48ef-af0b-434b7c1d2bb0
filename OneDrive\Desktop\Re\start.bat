@echo off
echo ========================================
echo    AUGURA CODER AGENT - QUICK START
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js 16+ and try again
    pause
    exit /b 1
)

echo ✓ Python and Node.js are available
echo.

REM Check if virtual environment exists
if not exist "agent_core\venv" (
    echo Creating Python virtual environment...
    cd agent_core
    python -m venv venv
    cd ..
    echo ✓ Virtual environment created
)

REM Activate virtual environment and install dependencies
echo Installing Python dependencies...
cd agent_core
call venv\Scripts\activate.bat
pip install -r requirements.txt
cd ..
echo ✓ Python dependencies installed
echo.

REM Install Node.js dependencies
echo Installing Node.js dependencies...
npm install
echo ✓ Node.js dependencies installed
echo.

REM Check for API keys
if not exist ".env" (
    echo Creating .env file template...
    echo # Augura Coder Agent Configuration > .env
    echo # Add your API keys below >> .env
    echo OPENROUTER_API_KEY=your_openrouter_key_here >> .env
    echo GROQ_API_KEY=your_groq_key_here >> .env
    echo. >> .env
    echo # Optional settings >> .env
    echo DEBUG_MODE=false >> .env
    echo LOG_LEVEL=INFO >> .env
    echo.
    echo ⚠️  Please edit .env file and add your API keys
    echo.
)

echo ========================================
echo    SETUP COMPLETE!
echo ========================================
echo.
echo Next steps:
echo 1. Edit .env file and add your API keys
echo 2. Open VS Code in this directory
echo 3. Press F5 to run the extension
echo 4. Or run: code .
echo.
echo For manual testing:
echo   cd agent_core
echo   venv\Scripts\activate.bat
echo   python main.py . --interactive
echo.
pause
