#!/bin/bash

echo "========================================"
echo "   AUGURA CODER AGENT - QUICK START"
echo "========================================"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "ERROR: Python 3 is not installed"
    echo "Please install Python 3.8+ and try again"
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed"
    echo "Please install Node.js 16+ and try again"
    exit 1
fi

echo "✓ Python and Node.js are available"
echo

# Check if virtual environment exists
if [ ! -d "agent_core/venv" ]; then
    echo "Creating Python virtual environment..."
    cd agent_core
    python3 -m venv venv
    cd ..
    echo "✓ Virtual environment created"
fi

# Activate virtual environment and install dependencies
echo "Installing Python dependencies..."
cd agent_core
source venv/bin/activate
pip install -r requirements.txt
cd ..
echo "✓ Python dependencies installed"
echo

# Install Node.js dependencies
echo "Installing Node.js dependencies..."
npm install
echo "✓ Node.js dependencies installed"
echo

# Check for API keys
if [ ! -f ".env" ]; then
    echo "Creating .env file template..."
    cat > .env << EOF
# Augura Coder Agent Configuration
# Add your API keys below
OPENROUTER_API_KEY=your_openrouter_key_here
GROQ_API_KEY=your_groq_key_here

# Optional settings
DEBUG_MODE=false
LOG_LEVEL=INFO
EOF
    echo
    echo "⚠️  Please edit .env file and add your API keys"
    echo
fi

echo "========================================"
echo "    SETUP COMPLETE!"
echo "========================================"
echo
echo "Next steps:"
echo "1. Edit .env file and add your API keys"
echo "2. Open VS Code in this directory"
echo "3. Press F5 to run the extension"
echo "4. Or run: code ."
echo
echo "For manual testing:"
echo "  cd agent_core"
echo "  source venv/bin/activate"
echo "  python main.py . --interactive"
echo
