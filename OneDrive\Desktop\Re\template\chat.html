<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Augura Chat</title>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dompurify@3.0.6/dist/purify.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bidi-js@1.0.3/lib/bidi.min.js"></script>

    <!-- Prism.js for syntax highlighting -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.min.css">
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/prism.min.js" data-manual></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>

    <!-- VS Code Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@vscode/codicons@0.0.35/dist/codicon.css">
    <link rel="stylesheet" href="styles/style.css">
</head>
<body>
    <div class="header">
        <div class="header-top">
            <button class="threads-toggle-btn" onclick="toggleThreadsPanel()" id="threadsToggleBtn">
                <i class="codicon codicon-chevron-right" id="threadsChevron"></i>
                <span>Threads</span>
            </button>
            <div class="header-actions">
                <button class="header-btn new-chat-btn" onclick="createNewChat()" title="محادثة جديدة">
                    <i class="codicon codicon-add"></i>
                </button>
                <button class="header-btn clear-btn" onclick="clearChat()" title="مسح المحادثة">
                    <i class="codicon codicon-trash"></i>
                </button>
            </div>
        </div>
        <div class="threads-container" id="threadsContainer">
            <div class="threads-section">
                <div class="threads-header">اليوم</div>
                <div class="threads-content" id="todayThreads"></div>
            </div>
            <div class="threads-section">
                <div class="threads-header">آخر 7 أيام</div>
                <div class="threads-content" id="weekThreads"></div>
            </div>
        </div>
    </div>

    <div class="chat-container" id="chatContainer">
        <div class="welcome-message">
            <i class="codicon codicon-lightbulb welcome-icon"></i>
            <div class="welcome-text">
                <strong>Hello! I'm Augura Coder</strong><br>
                Your AI programming assistant powered by advanced language models.<br>
                Ask me anything about coding, and I'll help you out!
            </div>
        </div>
        <div class="typing-indicator" id="typingIndicator">
            <span class="typing-dots">Thinking</span>
        </div>
    </div>

    <!-- Scroll to bottom button -->
    <button class="scroll-to-bottom" id="scrollToBottomBtn" onclick="forceScrollToBottom()" title="Scroll to bottom">
        <i class="codicon codicon-arrow-down"></i>
    </button>

    <div class="files-changed-section" id="filesChangedSection">
        <div class="files-changed-header">
            <div class="header-left">
                <i class="codicon codicon-chevron-right files-changed-toggle" id="filesToggle" onclick="toggleFilesChanged(event)"></i>
                <i class="codicon codicon-list-unordered files-changed-icon"></i>
                <span class="files-changed-text" id="filesChangedText">0 files changed</span>
            </div>
            <div class="header-right">
                <div class="files-changed-stats" id="filesChangedStats">
                    <span class="stats-added">+0</span>
                    <span class="stats-deleted">-0</span>
                </div>
                <div class="files-changed-actions" id="filesChangedActions" style="display: none;">
                    <button class="action-btn discard" onclick="alert('')" title="Discard All">
                        <i class="codicon codicon-discard"></i>
                        <span class="action-text">Discard All</span>
                    </button>
                    <button class="action-btn keep" onclick="alert('')" title="Keep All">
                        <i class="codicon codicon-check"></i>
                        <span class="action-text">Keep All</span>
                    </button>
                </div>
            </div>
        </div>
        <div class="files-changed-content" id="filesChangedContent">
            <div class="no-changes" id="noChangesMessage">
                No changes to show
            </div>
        </div>
    </div>

    <div class="input-container">
        <div class="input-row">
            <div class="input-wrapper">
                <i class="codicon codicon-comment-discussion input-icon"></i>
                <textarea
                    id="messageInput"
                    class="message-input"
                    placeholder="Ask or instruct Augura Coder..."
                    rows="1"
                ></textarea>
            </div>
            <button class="send-btn" onclick="sendMessage()" disabled title="Send message">
                <i class="codicon codicon-send btn-icon"></i>
            </button>
        </div>
    </div>

    <script src="scripts/loader.js"></script>
</body>
</html>
