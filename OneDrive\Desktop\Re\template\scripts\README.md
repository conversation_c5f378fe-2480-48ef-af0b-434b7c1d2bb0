# تقسيم ملف old.js إلى ملفات منفصلة

تم تقسيم ملف `old.js` الكبير (2022 سطر) إلى ملفات أصغر منظمة حسب الوظيفة لتحسين القابلية للقراءة والصيانة.

## الملفات المقسمة:

### 1. `main.js` - الملف الرئيسي والتهيئة
- تهيئة المتغيرات العامة
- إعداد vscode API
- إعداد marked و Prism.js
- معالج الرسائل الرئيسي من الإضافة
- مستمعي الأحداث الأساسية

### 2. `utils.js` - الوظائف المساعدة
- كشف اتجاه النص (RTL/LTR)
- أيقونات الملفات
- وظائف مساعدة عامة

### 3. `state-manager.js` - إدارة الحالة
- حفظ واستعادة حالة واجهة المستخدم
- إدارة التخزين المحلي
- حفظ مواضع التمرير والعناصر المتوسعة

### 4. `code-processor.js` - معالجة الكود وتمييز الصيغة
- معالجة كتل الكود
- تمييز الصيغة باستخدام Prism.js
- استخراج لغات البرمجة
- تبديل وإخفاء/إظهار كتل الكود
- نسخ الكود

### 5. `streaming-manager.js` - إدارة التدفق المباشر
- بدء وإنهاء الرسائل المتدفقة
- إضافة أجزاء النص المتدفق
- معالجة وضع التفكير
- إظهار مؤشر الكتابة

### 6. `message-manager.js` - إدارة الرسائل والدردشة
- إنشاء وعرض الرسائل
- تحرير الرسائل
- أزرار الإجراءات (إعجاب، عدم إعجاب، نسخ، إعادة توليد)
- معالجة محتوى Markdown
- إرسال الرسائل

### 7. `ui-manager.js` - إدارة واجهة المستخدم والتمرير
- إدارة التمرير الذكي
- زر التمرير إلى الأسفل
- تبديل لوحة الملفات المتغيرة
- مستمعي أحداث التمرير

### 8. `error-manager.js` - إدارة الأخطاء والإشعارات
- إشعارات إعادة المحاولة
- إشعارات الأخطاء
- عداد إعادة المحاولة
- إدارة الإشعارات المؤقتة

### 9. `threads-manager.js` - إدارة المحادثات
- تبديل لوحة المحادثات
- إنشاء وحذف المحادثات
- التبديل بين المحادثات
- عرض قائمة المحادثات

### 10. `agent-manager.js` - وضع الوكيل
- معالجة إجراءات الوكيل
- عرض رسائل الوكيل
- إدارة المهام الحالية
- أيقونات الوكيل

## كيفية الاستخدام:

### الطريقة الأولى: تحميل يدوي في HTML
```html
<script src="scripts/main.js"></script>
<script src="scripts/utils.js"></script>
<script src="scripts/state-manager.js"></script>
<script src="scripts/code-processor.js"></script>
<script src="scripts/streaming-manager.js"></script>
<script src="scripts/message-manager.js"></script>
<script src="scripts/ui-manager.js"></script>
<script src="scripts/error-manager.js"></script>
<script src="scripts/threads-manager.js"></script>
<script src="scripts/agent-manager.js"></script>
```

### الطريقة الثانية: استخدام loader.js
```html
<script src="scripts/loader.js"></script>
```

### الطريقة الثالثة: استخدام الملف الأصلي
```html
<script src="scripts/old.js"></script>
```

## ملاحظات مهمة:

1. **ترتيب التحميل مهم**: يجب تحميل `main.js` أولاً لأنه يحتوي على المتغيرات العامة
2. **التبعيات**: بعض الملفات تعتمد على وظائف في ملفات أخرى
3. **الأداء**: تقسيم الملفات يحسن من تنظيم الكود ولكن قد يزيد من عدد طلبات HTTP
4. **الصيانة**: الآن يمكن تعديل كل وظيفة في ملف منفصل دون التأثير على الوظائف الأخرى

## الفوائد من التقسيم:

- ✅ **تحسين القابلية للقراءة**: كل ملف يركز على وظيفة محددة
- ✅ **سهولة الصيانة**: يمكن تعديل وظيفة واحدة دون التأثير على الباقي
- ✅ **التطوير التعاوني**: يمكن لعدة مطورين العمل على ملفات مختلفة
- ✅ **إعادة الاستخدام**: يمكن استخدام ملفات معينة في مشاريع أخرى
- ✅ **التنظيم**: كل وظيفة في مكانها المناسب
- ✅ **التصحيح**: أسهل في العثور على الأخطاء وإصلاحها
