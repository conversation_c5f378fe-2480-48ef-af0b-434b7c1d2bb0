// ===== AUGURA AGENT INTEGRATION =====
// This file integrates the Python AI Agent with the existing chat interface

class AuguraAgent {
    constructor() {
        this.isInitialized = false;
        this.isProcessing = false;
        this.currentStreamingId = null;
        this.projectContext = null;
        this.conversationHistory = [];
        this.agentStats = {};
        
        // Configuration
        this.config = {
            openRouterApiKey: '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',
            groqApiKey: '********************************************************',
            defaultModel: 'openrouter/horizon-beta',
            groqModel: 'qwen/qwen3-32b'
        };
        
        this.initialize();
    }
    
    async initialize() {
        try {
            console.log('Initializing Augura Agent...');
            
            // Request project context from VS Code extension
            vscode.postMessage({
                type: 'initializeAgent',
                config: this.config
            });
            
            // Request current project information
            vscode.postMessage({
                type: 'getProjectContext'
            });
            
            this.isInitialized = true;
            console.log('✓ Augura Agent initialized');
            
            // Show initialization message
            this.addSystemMessage('🤖 Augura Coder Agent initialized and ready to help!');
            
        } catch (error) {
            console.error('Error initializing Augura Agent:', error);
            this.addSystemMessage('❌ Failed to initialize Augura Agent: ' + error.message);
        }
    }
    
    async processMessage(message, options = {}) {
        if (this.isProcessing) {
            this.addSystemMessage('⏳ Agent is currently processing another request. Please wait...');
            return;
        }
        
        this.isProcessing = true;
        
        try {
            // Add user message to chat
            this.addUserMessage(message);
            
            // Prepare context
            const context = this.prepareContext(message, options);
            
            // Start streaming response
            const messageId = this.startStreamingResponse();
            
            // Send to Python agent via VS Code extension
            vscode.postMessage({
                type: 'processAgentQuery',
                query: message,
                context: context,
                messageId: messageId,
                streaming: true
            });
            
        } catch (error) {
            console.error('Error processing message:', error);
            this.addSystemMessage('❌ Error processing message: ' + error.message);
        } finally {
            this.isProcessing = false;
        }
    }
    
    prepareContext(message, options = {}) {
        const context = {
            timestamp: Date.now(),
            conversationHistory: this.conversationHistory.slice(-5), // Last 5 messages
            projectContext: this.projectContext,
            userOptions: options
        };
        
        // Add current file context if available
        if (options.currentFile) {
            context.currentFile = options.currentFile;
        }
        
        // Add selected code if available
        if (options.selectedCode) {
            context.selectedCode = options.selectedCode;
        }
        
        return context;
    }
    
    startStreamingResponse() {
        const messageId = 'agent-' + Date.now();
        this.currentStreamingId = messageId;
        
        // Show typing indicator
        showTyping(true);
        
        // Create streaming message container
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant-message streaming';
        messageDiv.id = 'streaming-message-' + messageId;
        messageDiv.innerHTML = `
            <div class="message-header">
                <div class="message-role">
                    <i class="codicon codicon-robot"></i>
                    <span>Augura Coder</span>
                    <span class="agent-badge">AI Agent</span>
                </div>
                <div class="message-actions">
                    <button class="action-btn" onclick="copyMessage('${messageId}')" title="Copy">
                        <i class="codicon codicon-copy"></i>
                    </button>
                    <button class="action-btn" onclick="regenerateMessage('${messageId}')" title="Regenerate">
                        <i class="codicon codicon-refresh"></i>
                    </button>
                </div>
            </div>
            <div class="message-content" id="streaming-content-${messageId}">
                <div class="typing-indicator">
                    <span class="typing-dots">Thinking with AI...</span>
                </div>
            </div>
        `;
        
        chatContainer.appendChild(messageDiv);
        scrollToBottom();
        
        return messageId;
    }
    
    addStreamChunk(messageId, chunk) {
        if (messageId !== this.currentStreamingId) return;
        
        const contentDiv = document.getElementById('streaming-content-' + messageId);
        if (!contentDiv) return;
        
        // Remove typing indicator if it exists
        const typingIndicator = contentDiv.querySelector('.typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
        
        // Add chunk to content
        if (!contentDiv.querySelector('.streaming-text')) {
            contentDiv.innerHTML = '<div class="streaming-text"></div>';
        }
        
        const streamingText = contentDiv.querySelector('.streaming-text');
        streamingText.textContent += chunk;
        
        scrollToBottom();
    }
    
    endStreamingResponse(messageId, finalContent = null) {
        if (messageId !== this.currentStreamingId) return;
        
        const messageDiv = document.getElementById('streaming-message-' + messageId);
        const contentDiv = document.getElementById('streaming-content-' + messageId);
        
        if (messageDiv && contentDiv) {
            messageDiv.classList.remove('streaming');
            
            let content = finalContent;
            if (!content) {
                const streamingText = contentDiv.querySelector('.streaming-text');
                content = streamingText ? streamingText.textContent : '';
            }
            
            // Parse markdown and highlight code
            contentDiv.innerHTML = marked.parse(content);
            
            // Re-highlight code blocks
            contentDiv.querySelectorAll('pre code').forEach(block => {
                if (window.Prism) {
                    Prism.highlightElement(block);
                }
            });
            
            // Add code block actions
            this.addCodeBlockActions(contentDiv);
            
            // Add to conversation history
            this.conversationHistory.push({
                role: 'assistant',
                content: content,
                timestamp: Date.now(),
                messageId: messageId
            });
        }
        
        this.currentStreamingId = null;
        showTyping(false);
        scrollToBottom();
    }
    
    addUserMessage(message) {
        const messageId = 'user-' + Date.now();
        
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message user-message';
        messageDiv.id = 'message-' + messageId;
        
        messageDiv.innerHTML = `
            <div class="message-header">
                <div class="message-role">
                    <i class="codicon codicon-person"></i>
                    <span>You</span>
                </div>
                <div class="message-actions">
                    <button class="action-btn" onclick="copyMessage('${messageId}')" title="Copy">
                        <i class="codicon codicon-copy"></i>
                    </button>
                </div>
            </div>
            <div class="message-content">${message}</div>
        `;
        
        chatContainer.appendChild(messageDiv);
        
        // Add to conversation history
        this.conversationHistory.push({
            role: 'user',
            content: message,
            timestamp: Date.now(),
            messageId: messageId
        });
        
        scrollToBottom();
    }
    
    addSystemMessage(message, type = 'info') {
        const messageId = 'system-' + Date.now();
        
        const messageDiv = document.createElement('div');
        messageDiv.className = `message system-message ${type}`;
        messageDiv.id = 'message-' + messageId;
        
        const icon = type === 'error' ? 'codicon-error' : 
                    type === 'warning' ? 'codicon-warning' : 
                    'codicon-info';
        
        messageDiv.innerHTML = `
            <div class="message-header">
                <div class="message-role">
                    <i class="codicon ${icon}"></i>
                    <span>System</span>
                </div>
            </div>
            <div class="message-content">${message}</div>
        `;
        
        chatContainer.appendChild(messageDiv);
        scrollToBottom();
    }
    
    addCodeBlockActions(container) {
        const codeBlocks = container.querySelectorAll('pre code');
        codeBlocks.forEach((block, index) => {
            const pre = block.parentElement;
            if (!pre.querySelector('.code-actions')) {
                const actions = document.createElement('div');
                actions.className = 'code-actions';
                actions.innerHTML = `
                    <button class="code-action-btn" onclick="auguraAgent.copyCodeBlock(this)" title="Copy Code">
                        <i class="codicon codicon-copy"></i>
                    </button>
                    <button class="code-action-btn" onclick="auguraAgent.applyCodeBlock(this)" title="Apply Code">
                        <i class="codicon codicon-check"></i>
                    </button>
                    <button class="code-action-btn" onclick="auguraAgent.createFileFromCode(this)" title="Create File">
                        <i class="codicon codicon-new-file"></i>
                    </button>
                `;
                pre.appendChild(actions);
            }
        });
    }
    
    copyCodeBlock(button) {
        const pre = button.closest('pre');
        const code = pre.querySelector('code').textContent;
        
        navigator.clipboard.writeText(code).then(() => {
            // Show feedback
            const originalIcon = button.innerHTML;
            button.innerHTML = '<i class="codicon codicon-check"></i>';
            setTimeout(() => {
                button.innerHTML = originalIcon;
            }, 1000);
        });
    }
    
    applyCodeBlock(button) {
        const pre = button.closest('pre');
        const code = pre.querySelector('code').textContent;
        const language = pre.querySelector('code').className.replace('language-', '');
        
        vscode.postMessage({
            type: 'applyCode',
            code: code,
            language: language
        });
        
        this.addSystemMessage('✅ Code applied to current file');
    }
    
    createFileFromCode(button) {
        const pre = button.closest('pre');
        const code = pre.querySelector('code').textContent;
        const language = pre.querySelector('code').className.replace('language-', '');
        
        // Prompt for filename
        const filename = prompt('Enter filename:', this.suggestFilename(language));
        if (filename) {
            vscode.postMessage({
                type: 'createFile',
                filename: filename,
                content: code,
                language: language
            });
            
            this.addSystemMessage(`✅ Created file: ${filename}`);
        }
    }
    
    suggestFilename(language) {
        const extensions = {
            'javascript': '.js',
            'typescript': '.ts',
            'python': '.py',
            'java': '.java',
            'cpp': '.cpp',
            'c': '.c',
            'html': '.html',
            'css': '.css',
            'json': '.json'
        };
        
        const ext = extensions[language] || '.txt';
        return `new_file${ext}`;
    }
    
    showAnalysisResult(analysis) {
        const messageId = 'analysis-' + Date.now();
        
        let content = `## 📊 Code Analysis Results\n\n`;
        content += `**File:** ${analysis.filePath}\n`;
        content += `**Language:** ${analysis.language}\n\n`;
        
        // Metrics
        if (analysis.metrics) {
            content += `### 📈 Metrics\n`;
            content += `- Total Lines: ${analysis.metrics.totalLines}\n`;
            content += `- Code Lines: ${analysis.metrics.codeLines}\n`;
            content += `- Comment Lines: ${analysis.metrics.commentLines}\n`;
            content += `- Complexity: ${analysis.complexity?.cyclomatic || 'N/A'}\n\n`;
        }
        
        // Structure
        if (analysis.structure) {
            if (analysis.structure.functions?.length > 0) {
                content += `### 🔧 Functions (${analysis.structure.functions.length})\n`;
                analysis.structure.functions.slice(0, 5).forEach(func => {
                    content += `- \`${func.name}\` (line ${func.line})\n`;
                });
                if (analysis.structure.functions.length > 5) {
                    content += `- ... and ${analysis.structure.functions.length - 5} more\n`;
                }
                content += `\n`;
            }
            
            if (analysis.structure.classes?.length > 0) {
                content += `### 📦 Classes (${analysis.structure.classes.length})\n`;
                analysis.structure.classes.slice(0, 5).forEach(cls => {
                    content += `- \`${cls.name}\` (line ${cls.line})\n`;
                });
                if (analysis.structure.classes.length > 5) {
                    content += `- ... and ${analysis.structure.classes.length - 5} more\n`;
                }
                content += `\n`;
            }
        }
        
        // Issues
        if (analysis.issues?.length > 0) {
            content += `### ⚠️ Issues Found\n`;
            analysis.issues.slice(0, 5).forEach(issue => {
                content += `- **Line ${issue.line}:** ${issue.message}\n`;
            });
            if (analysis.issues.length > 5) {
                content += `- ... and ${analysis.issues.length - 5} more issues\n`;
            }
            content += `\n`;
        }
        
        // Suggestions
        if (analysis.suggestions?.length > 0) {
            content += `### 💡 Suggestions\n`;
            analysis.suggestions.forEach(suggestion => {
                content += `- **${suggestion.type}:** ${suggestion.message}\n`;
            });
        }
        
        this.addAssistantMessage(content, messageId);
    }
    
    showRefactoringSuggestions(suggestions) {
        const messageId = 'refactor-' + Date.now();
        
        let content = `## 🔄 Refactoring Suggestions\n\n`;
        
        if (suggestions.content) {
            content += suggestions.content;
        } else {
            content += `No specific suggestions available.`;
        }
        
        this.addAssistantMessage(content, messageId);
    }
    
    showCodeChanges(changes) {
        const messageId = 'changes-' + Date.now();
        
        let content = `## ✅ Code Changes Applied\n\n`;
        
        changes.forEach((change, index) => {
            content += `### Change ${index + 1}: ${change.type}\n`;
            content += `**File:** ${change.file}\n`;
            if (change.description) {
                content += `**Description:** ${change.description}\n`;
            }
            content += `\n`;
        });
        
        this.addAssistantMessage(content, messageId);
    }
    
    addAssistantMessage(content, messageId = null) {
        if (!messageId) {
            messageId = 'assistant-' + Date.now();
        }
        
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant-message';
        messageDiv.id = 'message-' + messageId;
        
        messageDiv.innerHTML = `
            <div class="message-header">
                <div class="message-role">
                    <i class="codicon codicon-robot"></i>
                    <span>Augura Coder</span>
                    <span class="agent-badge">AI Agent</span>
                </div>
                <div class="message-actions">
                    <button class="action-btn" onclick="copyMessage('${messageId}')" title="Copy">
                        <i class="codicon codicon-copy"></i>
                    </button>
                    <button class="action-btn" onclick="regenerateMessage('${messageId}')" title="Regenerate">
                        <i class="codicon codicon-refresh"></i>
                    </button>
                </div>
            </div>
            <div class="message-content">
                ${marked.parse(content)}
            </div>
        `;
        
        chatContainer.appendChild(messageDiv);
        
        // Highlight code blocks
        messageDiv.querySelectorAll('pre code').forEach(block => {
            if (window.Prism) {
                Prism.highlightElement(block);
            }
        });
        
        // Add code block actions
        this.addCodeBlockActions(messageDiv);
        
        // Add to conversation history
        this.conversationHistory.push({
            role: 'assistant',
            content: content,
            timestamp: Date.now(),
            messageId: messageId
        });
        
        scrollToBottom();
    }
    
    updateProjectContext(context) {
        this.projectContext = context;
        
        // Update UI to show project info
        const welcomeMessage = document.querySelector('.welcome-message');
        if (welcomeMessage && context) {
            let projectInfo = welcomeMessage.querySelector('.project-info');
            if (!projectInfo) {
                projectInfo = document.createElement('div');
                projectInfo.className = 'project-info';
                welcomeMessage.appendChild(projectInfo);
            }
            
            projectInfo.innerHTML = `
                <div class="project-details">
                    <h4>📁 Project: ${context.name || 'Unknown'}</h4>
                    <p><strong>Files:</strong> ${context.totalFiles || 0}</p>
                    <p><strong>Languages:</strong> ${Object.keys(context.languages || {}).join(', ')}</p>
                </div>
            `;
        }
    }
    
    showError(error) {
        this.addSystemMessage(`❌ ${error.message}`, 'error');
        if (error.details) {
            console.error('Agent Error Details:', error.details);
        }
    }
    
    getStats() {
        return {
            conversationHistory: this.conversationHistory.length,
            isInitialized: this.isInitialized,
            isProcessing: this.isProcessing,
            projectContext: this.projectContext ? 'loaded' : 'not loaded',
            agentStats: this.agentStats
        };
    }
}

// Initialize the agent
const auguraAgent = new AuguraAgent();

// Override the original sendMessage function to use the agent
const originalSendMessage = window.sendMessage;
window.sendMessage = function() {
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();
    
    if (!message) return;
    
    // Clear input
    messageInput.value = '';
    updateSendButton();
    
    // Get current context
    const context = {
        currentFile: getCurrentFileContext(),
        selectedCode: getSelectedCode()
    };
    
    // Process with agent
    auguraAgent.processMessage(message, context);
};

// Helper functions
function getCurrentFileContext() {
    // This would be populated by the VS Code extension
    return window.currentFileContext || null;
}

function getSelectedCode() {
    // This would be populated by the VS Code extension
    return window.selectedCode || null;
}

// Message handlers for VS Code extension communication
window.addEventListener('message', event => {
    const message = event.data;
    
    switch (message.type) {
        case 'agentStreamStart':
            // Already handled in startStreamingResponse
            break;
            
        case 'agentStreamChunk':
            auguraAgent.addStreamChunk(message.messageId, message.chunk);
            break;
            
        case 'agentStreamEnd':
            auguraAgent.endStreamingResponse(message.messageId, message.finalContent);
            break;
            
        case 'agentAnalysis':
            auguraAgent.showAnalysisResult(message.analysis);
            break;
            
        case 'agentRefactoringSuggestions':
            auguraAgent.showRefactoringSuggestions(message.suggestions);
            break;
            
        case 'agentCodeChanges':
            auguraAgent.showCodeChanges(message.changes);
            break;
            
        case 'agentProjectContext':
            auguraAgent.updateProjectContext(message.context);
            break;
            
        case 'agentError':
            auguraAgent.showError(message.error);
            break;
            
        case 'agentStats':
            auguraAgent.agentStats = message.stats;
            break;
    }
});

// Add agent-specific CSS styles
const agentStyles = `
<style>
.agent-badge {
    background: linear-gradient(45deg, #007acc, #0099cc);
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: bold;
    margin-left: 8px;
    text-transform: uppercase;
}

.system-message {
    background: rgba(255, 193, 7, 0.1);
    border-left: 3px solid #ffc107;
    margin: 8px 0;
    padding: 8px 12px;
    border-radius: 4px;
}

.system-message.error {
    background: rgba(220, 53, 69, 0.1);
    border-left-color: #dc3545;
}

.system-message.warning {
    background: rgba(255, 193, 7, 0.1);
    border-left-color: #ffc107;
}

.code-actions {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s;
}

pre:hover .code-actions {
    opacity: 1;
}

.code-action-btn {
    background: rgba(0, 0, 0, 0.7);
    border: none;
    color: white;
    padding: 4px 6px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    transition: background 0.2s;
}

.code-action-btn:hover {
    background: rgba(0, 0, 0, 0.9);
}

.project-info {
    background: rgba(0, 122, 204, 0.1);
    border: 1px solid rgba(0, 122, 204, 0.3);
    border-radius: 8px;
    padding: 12px;
    margin-top: 12px;
}

.project-details h4 {
    margin: 0 0 8px 0;
    color: #007acc;
}

.project-details p {
    margin: 4px 0;
    font-size: 14px;
}

.typing-dots {
    background: linear-gradient(45deg, #007acc, #0099cc);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
}

.streaming .message-content {
    border-left: 3px solid #007acc;
    padding-left: 12px;
}
</style>
`;

// Inject styles
document.head.insertAdjacentHTML('beforeend', agentStyles);

// Export for global access
window.auguraAgent = auguraAgent;
