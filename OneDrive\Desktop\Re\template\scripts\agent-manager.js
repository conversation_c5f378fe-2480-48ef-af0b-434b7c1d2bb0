// ===== AGENT MODE FUNCTIONS =====

/**
 * Handle agent actions
 */
function handleAgentAction(action, data) {
    switch (action) {
        case 'fileRead':
            showAgentMessage(`📖 Reading file: ${data.file}`, 'action');
            break;

        case 'codeAnalysis':
            showAgentMessage(`🔍 Analyzing code in: ${data.file}`, 'action');
            break;
        case 'codeGeneration':
            showAgentMessage(`🚀 Generating code for: ${data.description}`, 'action');
            break;
        case 'codeRefactor':
            showAgentMessage(`🔧 Refactoring code in: ${data.file}`, 'action');
            break;
        case 'commandExecute':
            showAgentMessage(`⚡ Executing command: ${data.command}`, 'action');
            break;
        case 'thinking':
            showAgentMessage(`🤔 ${data.message}`, 'thinking');
            break;
        default:
            console.log('Unknown agent action:', action);
    }
}

/**
 * Update current task display
 */
function updateCurrentTask(task) {
    currentTask = task;

    if (task) {
        showAgentMessage(`📋 Current task: ${task.description}`, 'task');

        // Update UI to show current task
        const taskElement = document.createElement('div');
        taskElement.className = 'current-task';
        taskElement.innerHTML = `
            <div class="task-header">
                <i class="codicon codicon-checklist"></i>
                <span>Current Task</span>
            </div>
            <div class="task-content">
                <div class="task-title">${task.title || 'Untitled Task'}</div>
                <div class="task-description">${task.description}</div>
                <div class="task-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${task.progress || 0}%"></div>
                    </div>
                    <span class="progress-text">${task.progress || 0}%</span>
                </div>
            </div>
        `;

        // Add to chat container
        chatContainer.appendChild(taskElement);
        scrollToBottomIfEnabled();
    }
}

/**
 * Show agent-specific messages
 */
function showAgentMessage(message, type = 'info') {
    const messageElement = document.createElement('div');
    messageElement.className = `agent-message agent-${type}`;

    const icon = getAgentIcon(type);
    const timestamp = new Date().toLocaleTimeString();

    messageElement.innerHTML = `
        <div class="agent-message-content">
            <span class="agent-icon">${icon}</span>
            <span class="agent-text">${message}</span>
            <span class="agent-timestamp">${timestamp}</span>
        </div>
    `;

    chatContainer.appendChild(messageElement);
    scrollToBottomIfEnabled();

    // Auto-remove action messages after 5 seconds
    if (type === 'action' || type === 'thinking') {
        setTimeout(() => {
            if (messageElement.parentNode) {
                messageElement.remove();
            }
        }, 5000);
    }
}

/**
 * Get icon for agent message type
 */
function getAgentIcon(type) {
    const icons = {
        'action': '⚡',
        'thinking': '🤔',
        'task': '📋',
        'file-change': '📝',
        'success': '✅',
        'error': '❌',
        'warning': '⚠️',
        'info': 'ℹ️'
    };

    return icons[type] || 'ℹ️';
}
