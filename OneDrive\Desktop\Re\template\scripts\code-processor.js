// ===== CODE PROCESSING & SYNTAX HIGHLIGHTING =====

function processCodeBlocksInElement(element) {
    // Process code blocks
    const codeBlocks = element.querySelectorAll('pre');
    codeBlocks.forEach((pre, index) => {
        const code = pre.querySelector('code');
        if (!code) return;

        const codeId = 'code-' + Date.now() + '-' + index;
        const language = extractLanguage(code);
        const codeContent = code.textContent;

        const languageDisplay = language ? `<span class="code-block-language">${language}</span>` : '';

        const codeBlockHTML = `
                    <div class="code-block">
                        <div class="code-block-header">
                            <div class="code-block-title">
                                <i class="codicon codicon-chevron-right code-block-toggle" id="toggle-${codeId}" onclick="toggleCodeBlock('${codeId}', event)"></i>
                                <i class="codicon codicon-file-code"></i>
                                ${languageDisplay}
                            </div>
                            <div class="code-block-actions">
                                <button class="code-block-copy" onclick="copyCodeBlock('${codeId}')" title="Copy code">
                                    <i class="codicon codicon-copy"></i>
                                </button>
                            </div>
                        </div>
                        <div class="code-block-content" id="content-${codeId}">
                            <pre><code class="language-${language || 'text'}">${escapeHtml(codeContent)}</code></pre>
                        </div>
                    </div>
                `;

        pre.outerHTML = codeBlockHTML;
    });

    // Process tables - wrap them for horizontal scrolling
    const tables = element.querySelectorAll('table');
    tables.forEach(table => {
        if (!table.parentElement.classList.contains('table-wrapper')) {
            const wrapper = document.createElement('div');
            wrapper.className = 'table-wrapper';
            table.parentNode.insertBefore(wrapper, table);
            wrapper.appendChild(table);
        }
    });
}

// Helper functions for code blocks
function extractLanguage(codeElement) {
    const className = codeElement.className;
    const match = className.match(/language-(\w+)/);
    let language = match ? match[1] : null;

    // Comprehensive language mapping for all supported languages
    const languageMap = {
        // JavaScript family
        'js': 'javascript',
        'jsx': 'jsx',
        'ts': 'typescript',
        'tsx': 'tsx',
        'node': 'javascript',
        'nodejs': 'javascript',
        // Python family
        'py': 'python',
        'python3': 'python',
        'py3': 'python',
        // Web technologies
        'html': 'markup',
        'htm': 'markup',
        'xml': 'markup',
        'svg': 'markup',
        'mathml': 'markup',
        'ssml': 'markup',
        'atom': 'markup',
        'rss': 'markup',
        'xhtml': 'markup',

        // Stylesheets
        'css': 'css',
        'scss': 'scss',
        'sass': 'sass',
        'less': 'less',
        'stylus': 'stylus',

        // Shell/Command line
        'sh': 'bash',
        'shell': 'bash',
        'bash': 'bash',
        'zsh': 'bash',
        'fish': 'bash',
        'powershell': 'powershell',
        'ps1': 'powershell',
        'cmd': 'batch',
        'bat': 'batch',
        'batch': 'batch',

        // Data formats
        'json': 'json',
        'json5': 'json5',
        'jsonp': 'jsonp',
        'yaml': 'yaml',
        'yml': 'yaml',
        'toml': 'toml',
        'xml': 'markup',
        'csv': 'csv',

        // C family
        'c': 'c',
        'cpp': 'cpp',
        'c++': 'cpp',
        'cxx': 'cpp',
        'cc': 'cpp',
        'h': 'c',
        'hpp': 'cpp',
        'cs': 'csharp',
        'csharp': 'csharp',

        // Java family
        'java': 'java',
        'scala': 'scala',
        'kotlin': 'kotlin',
        'kt': 'kotlin',
        'groovy': 'groovy',

        // .NET
        'vb': 'vbnet',
        'vbnet': 'vbnet',
        'fsharp': 'fsharp',
        'fs': 'fsharp',

        // Mobile
        'swift': 'swift',
        'objc': 'objectivec',
        'objective-c': 'objectivec',
        'dart': 'dart',

        // Functional
        'haskell': 'haskell',
        'hs': 'haskell',
        'elm': 'elm',
        'clojure': 'clojure',
        'clj': 'clojure',
        'erlang': 'erlang',
        'elixir': 'elixir',
        'ocaml': 'ocaml',
        'reason': 'reason',
        'purescript': 'purescript',

        // Systems
        'rust': 'rust',
        'rs': 'rust',
        'go': 'go',
        'golang': 'go',
        'zig': 'zig',
        'nim': 'nim',
        'crystal': 'crystal',

        // Scripting
        'ruby': 'ruby',
        'rb': 'ruby',
        'php': 'php',
        'perl': 'perl',
        'pl': 'perl',
        'lua': 'lua',

        // Database
        'sql': 'sql',
        'mysql': 'sql',
        'postgresql': 'sql',
        'sqlite': 'sql',
        'plsql': 'plsql',

        // Markup/Documentation
        'markdown': 'markdown',
        'md': 'markdown',
        'tex': 'latex',
        'latex': 'latex',
        'rst': 'rest',
        'asciidoc': 'asciidoc',
        'adoc': 'asciidoc',

        // Configuration
        'ini': 'ini',
        'conf': 'ini',
        'config': 'ini',
        'properties': 'properties',
        'dockerfile': 'docker',
        'docker': 'docker',
        'makefile': 'makefile',
        'make': 'makefile',
        'cmake': 'cmake',
        'nginx': 'nginx',
        'apache': 'apacheconf',

        // Assembly
        'asm': 'nasm',
        'nasm': 'nasm',
        'assembly': 'nasm',

        // Other popular languages
        'r': 'r',
        'matlab': 'matlab',
        'julia': 'julia',
        'fortran': 'fortran',
        'cobol': 'cobol',
        'pascal': 'pascal',
        'ada': 'ada',
        'prolog': 'prolog',
        'scheme': 'scheme',
        'lisp': 'lisp',
        'smalltalk': 'smalltalk',

        // Game development
        'gdscript': 'gdscript',
        'hlsl': 'hlsl',
        'glsl': 'glsl',

        // Blockchain
        'solidity': 'solidity',
        'sol': 'solidity',

        // Query languages
        'graphql': 'graphql',
        'sparql': 'sparql',

        // Template engines
        'handlebars': 'handlebars',
        'hbs': 'handlebars',
        'mustache': 'handlebars',
        'twig': 'twig',
        'smarty': 'smarty',
        'pug': 'pug',
        'jade': 'pug',
        'ejs': 'ejs',

        // Version control
        'diff': 'diff',
        'patch': 'diff',
        'git': 'git',

        // Misc
        'regex': 'regex',
        'regexp': 'regex',
        'log': 'log',
        'text': 'text',
        'txt': 'text',
        'plain': 'text'
    };

    if (language && languageMap[language]) {
        language = languageMap[language];
    }

    return language;
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Function to apply syntax highlighting to dynamically added content
function applySyntaxHighlighting(element) {
    if (!window.Prism) {
        // If Prism is not loaded yet, try again after a short delay
        setTimeout(() => applySyntaxHighlighting(element), 100);
        return;
    }

    // Function to highlight a single code element
    function highlightCodeElement(code, isInline = false) {
        const language = extractLanguage(code);

        if (!language || language === 'text' || language === 'plain') {
            return; // Skip highlighting for plain text
        }

        // Check if already highlighted
        if (code.classList.contains('prism-highlighted')) {
            return;
        }

        // If language is available, highlight immediately
        if (window.Prism.languages[language]) {
            try {
                const highlighted = window.Prism.highlight(code.textContent, window.Prism.languages[language], language);
                code.innerHTML = highlighted;
                code.classList.add('prism-highlighted');

            } catch (e) {
                console.warn(`Failed to highlight ${language} code:`, e);
            }
        } else {
            // Language not loaded yet, try to load it with autoloader

            // Mark as pending to avoid multiple load attempts
            code.classList.add('prism-loading');

            // Use Prism's autoloader to load the language
            if (window.Prism.plugins && window.Prism.plugins.autoloader) {
                // Create a temporary element to trigger autoloader
                const tempElement = document.createElement('code');
                tempElement.className = `language-${language}`;
                tempElement.textContent = code.textContent;
                tempElement.style.display = 'none';
                document.body.appendChild(tempElement);

                // Try to highlight with autoloader
                try {
                    window.Prism.highlightElement(tempElement);
                } catch (e) {
                    console.warn(`Autoloader failed for ${language}:`, e);
                }

                // Clean up temp element
                document.body.removeChild(tempElement);

                // Check if language is now available and retry
                setTimeout(() => {
                    if (window.Prism.languages[language] && !code.classList.contains('prism-highlighted')) {
                        code.classList.remove('prism-loading');
                        highlightCodeElement(code, isInline);
                    } else {
                        // If still not available after timeout, mark as failed and remove loading class
                        code.classList.remove('prism-loading');
                        console.warn(`Language ${language} could not be loaded`);
                    }
                }, 1000);
            }
        }
    }

    // Highlight code blocks in .code-block-content
    const codeBlocks = element.querySelectorAll('.code-block-content code');
    codeBlocks.forEach(code => highlightCodeElement(code, false));

    // Highlight inline code elements (not in pre tags)
    const inlineCodes = element.querySelectorAll('code:not(pre code):not(.code-block-content code)');
    inlineCodes.forEach(code => highlightCodeElement(code, true));
}

// Function to re-highlight all existing code blocks
function rehighlightAllCode() {
    if (!window.Prism) {
        console.warn('Prism.js not loaded yet');
        return;
    }

    // Remove existing highlighting classes
    document.querySelectorAll('code.prism-highlighted, code.prism-loading').forEach(code => {
        code.classList.remove('prism-highlighted', 'prism-loading');
    });

    // Re-apply highlighting to all code blocks
    applySyntaxHighlighting(document.body);
}

function toggleCodeBlock(codeId, event) {
    // Prevent event bubbling if called from icon click
    if (event) {
        event.stopPropagation();
    }

    const content = document.getElementById('content-' + codeId);
    const toggle = document.getElementById('toggle-' + codeId);

    // Check if it's in default collapsed state (no classes) or explicitly collapsed
    const isCollapsed = !content.classList.contains('expanded');

    if (isCollapsed) {
        // Expand
        content.classList.add('expanded');
        content.classList.remove('collapsed');
        toggle.classList.remove('collapsed');
        // Change icon to chevron-down
        toggle.classList.remove('codicon-chevron-right');
        toggle.classList.add('codicon-chevron-down');
        // Save state
        uiState.expandedCodeBlocks.add(codeId);
    } else {
        // Collapse
        content.classList.remove('expanded');
        content.classList.add('collapsed');
        toggle.classList.add('collapsed');
        // Change icon to chevron-right
        toggle.classList.remove('codicon-chevron-down');
        toggle.classList.add('codicon-chevron-right');
        // Save state
        uiState.expandedCodeBlocks.delete(codeId);
    }

    // Save state immediately
    saveUIState();
}

function copyCodeBlock(codeId) {
    const content = document.getElementById('content-' + codeId);
    const code = content.querySelector('code').textContent;

    navigator.clipboard.writeText(code).then(() => {
        // Show feedback
        const copyBtn = content.parentElement.querySelector('.code-block-copy');
        const originalIcon = copyBtn.innerHTML;
        copyBtn.innerHTML = '<i class="codicon codicon-check"></i>';
        copyBtn.style.color = '#4ade80';

        setTimeout(() => {
            copyBtn.innerHTML = originalIcon;
            copyBtn.style.color = '';
        }, 1000);
    }).catch(err => {
        console.error('Failed to copy code:', err);
    });
}
