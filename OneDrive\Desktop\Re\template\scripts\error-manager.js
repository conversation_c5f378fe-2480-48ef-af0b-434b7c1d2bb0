// ===== ERROR & NOTIFICATION MANAGEMENT =====

// Enhanced retry notification system
let retryNotificationElement = null;

function showRetryNotification(attempt, maxAttempts, delaySeconds, errorType) {
    // Remove existing notification if any
    if (retryNotificationElement) {
        retryNotificationElement.remove();
        retryNotificationElement = null;
    }

    // Create notification element
    retryNotificationElement = document.createElement('div');
    retryNotificationElement.className = 'retry-notification';
    retryNotificationElement.innerHTML = `
        <div class="retry-content">
            <div class="retry-header">
                <i class="codicon codicon-warning"></i>
                <span class="retry-title">${errorType} Error</span>
            </div>
            <div class="retry-message">
                Retrying automatically in <span class="countdown-timer">${delaySeconds}</span> seconds...
            </div>
            <div class="retry-progress">
                <span class="retry-attempt">Attempt ${attempt} of ${maxAttempts}</span>
                <div class="retry-progress-bar">
                    <div class="retry-progress-fill" style="width: ${(attempt / maxAttempts) * 100}%"></div>
                </div>
            </div>
        </div>
    `;

    // Add to chat container
    chatContainer.appendChild(retryNotificationElement);
    scrollToBottomIfEnabled();

    // Start countdown
    startRetryCountdown(delaySeconds);
}

function startRetryCountdown(seconds) {
    let remaining = seconds;
    const timerElement = retryNotificationElement?.querySelector('.countdown-timer');

    const countdown = setInterval(() => {
        remaining--;
        if (timerElement) {
            timerElement.textContent = remaining;
        }

        if (remaining <= 0) {
            clearInterval(countdown);
            // Remove notification after countdown
            setTimeout(() => {
                if (retryNotificationElement) {
                    retryNotificationElement.remove();
                    retryNotificationElement = null;
                }
            }, 1000);
        }
    }, 1000);
}

// Legacy function for compatibility
function showRetryCountdown(remainingTime, attempt, maxAttempts) {
    showRetryNotification(attempt, maxAttempts, remainingTime, 'API');
}

function updateRetryCountdown(remainingTime) {
    // Legacy function - now handled by startRetryCountdown
}

// Error notification system
function showErrorNotification(errorType, message, status, retryable) {
    const errorElement = document.createElement('div');
    errorElement.className = 'error-notification';

    const errorTypeText = errorType.replace('_', ' ').toUpperCase();
    const statusText = status ? ` (${status})` : '';
    const retryableText = retryable ? 'This error will be retried automatically.' : 'Please check your configuration and try again.';

    errorElement.innerHTML = `
        <div class="error-content">
            <div class="error-header">
                <i class="codicon codicon-error"></i>
                <span class="error-title">${errorTypeText}${statusText}</span>
            </div>
            <div class="error-message">${message}</div>
            <div class="error-footer">
                <span class="error-hint">${retryableText}</span>
            </div>
        </div>
    `;

    // Add to chat container
    chatContainer.appendChild(errorElement);
    scrollToBottomIfEnabled();

    // Auto-remove after 10 seconds for retryable errors, 30 seconds for non-retryable
    const autoRemoveDelay = retryable ? 10000 : 30000;
    setTimeout(() => {
        if (errorElement && errorElement.parentNode) {
            errorElement.remove();
        }
    }, autoRemoveDelay);
}

function showRetryThinking() {
    // Remove countdown
    if (retryNotificationElement) {
        retryNotificationElement.remove();
        retryNotificationElement = null;
    }

    // Show thinking indicator
    showTyping(true);
}
