// ===== SCRIPT LOADER =====
// هذا الملف يحتوي على تعليمات لتحميل جميع الملفات المقسمة بالترتيب الصحيح

/*
لاستخدام الملفات المقسمة، يجب تحميلها بالترتيب التالي في ملف HTML:

<script src="scripts/main.js"></script>
<script src="scripts/utils.js"></script>
<script src="scripts/state-manager.js"></script>
<script src="scripts/code-processor.js"></script>
<script src="scripts/streaming-manager.js"></script>
<script src="scripts/message-manager.js"></script>
<script src="scripts/ui-manager.js"></script>
<script src="scripts/error-manager.js"></script>
<script src="scripts/threads-manager.js"></script>
<script src="scripts/agent-manager.js"></script>

أو يمكن استخدام هذا الكود JavaScript لتحميلها ديناميكياً:
*/

function loadScripts() {
    const scripts = [
        'scripts/main.js',
        'scripts/utils.js', 
        'scripts/state-manager.js',
        'scripts/code-processor.js',
        'scripts/streaming-manager.js',
        'scripts/message-manager.js',
        'scripts/ui-manager.js',
        'scripts/error-manager.js',
        'scripts/threads-manager.js',
        'scripts/agent-manager.js'
    ];

    let loadedCount = 0;
    
    function loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = () => {
                console.log(`✅ Loaded: ${src}`);
                resolve();
            };
            script.onerror = () => {
                console.error(`❌ Failed to load: ${src}`);
                reject(new Error(`Failed to load script: ${src}`));
            };
            document.head.appendChild(script);
        });
    }

    // تحميل الملفات بالتسلسل
    async function loadAllScripts() {
        try {
            for (const script of scripts) {
                await loadScript(script);
                loadedCount++;
                console.log(`📊 Progress: ${loadedCount}/${scripts.length} scripts loaded`);
            }
            console.log('🎉 All scripts loaded successfully!');
        } catch (error) {
            console.error('💥 Error loading scripts:', error);
        }
    }

    loadAllScripts();
}

// استدعاء الدالة عند تحميل الصفحة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', loadScripts);
} else {
    loadScripts();
}
