// ===== MAIN INITIALIZATION =====
const vscode = acquireVsCodeApi();
const chatContainer = document.getElementById('chatContainer');
const messageInput = document.getElementById('messageInput');
const typingIndicator = document.getElementById('typingIndicator');
const scrollToBottomBtn = document.getElementById('scrollToBottomBtn');

// Global state variables
let currentThreads = [];
let threadsPanelOpen = false;
let currentStreamingMessage = null;
let isInThinkingMode = false;
let responseBuffer = '';
let agentMode = true; // Enable agent mode
let currentTask = null;

// Scroll management variables
let userIsScrolling = false;
let autoScrollEnabled = true;
let scrollTimeout = null;

// State management for UI persistence
let uiState = {
    scrollPosition: 0,
    expandedCodeBlocks: new Set(),
    filesChangedExpanded: false
};

// Configure marked globally
marked.setOptions({
    breaks: true,
    gfm: true,
    tables: true,
    smartLists: true,
    smartypants: true
});

// Configure Prism.js for syntax highlighting with all languages support
window.Prism = window.Prism || {};
window.Prism.manual = true; // Disable automatic highlighting

// Configure autoloader for all languages
document.addEventListener('DOMContentLoaded', () => {
    if (window.Prism && window.Prism.plugins && window.Prism.plugins.autoloader) {
        // Set up autoloader path for all languages
        window.Prism.plugins.autoloader.languages_path = 'https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/';

        // Configure autoloader to load any language automatically
        window.Prism.plugins.autoloader.use_minified = true;

        // Re-highlight any existing code blocks after a short delay
        setTimeout(() => {
            rehighlightAllCode();
        }, 500);
    } else {
        console.warn('Prism.js or autoloader not available');
    }
});

// Request saved messages from extension
vscode.postMessage({
    type: 'requestSavedMessages'
});

// Save UI state periodically
setInterval(() => {
    saveUIState();
}, 2000);

// Main message handler from extension
window.addEventListener('message', event => {
    const message = event.data;

    switch (message.type) {
        case 'streamStart':
            startStreamingMessage(message.role, message.messageId);
            break;
        case 'streamChunk':
            addStreamChunk(message.chunk);
            break;
        case 'streamEnd':
            endStreamingMessage();
            break;
        case 'clearMessages':
            clearMessages();
            break;
        case 'typing':
            showTyping(message.isTyping);
            break;
        case 'savedMessages':
        case 'restoreMessages':
            restoreSavedMessages(message.messages);
            break;
        case 'showRetryCountdown':
            showRetryCountdown(message.remainingTime, message.attempt, message.maxAttempts);
            break;
        case 'updateRetryCountdown':
            updateRetryCountdown(message.remainingTime);
            break;
        case 'showRetryThinking':
            showRetryThinking();
            break;
        case 'removeMessage':
            removeMessage(message.messageId);
            break;
        case 'updateMessageReaction':
            updateMessageReaction(message.messageId, message.liked, message.disliked);
            break;
        case 'agentAction':
            handleAgentAction(message.action, message.data);
            break;
        case 'taskUpdate':
            updateCurrentTask(message.task);
            break;
        case 'threadsUpdated':
            updateThreadsList(message.threads);
            break;
        case 'threadSwitched':
            handleThreadSwitch(message.threadId);
            break;
        case 'editMessageComplete':
            handleEditMessageComplete(message.messageId, message.newContent);
            break;
        case 'markFinalMessage':
            markMessageAsFinal(message.messageId);
            break;
        case 'retryNotification':
            showRetryNotification(message.attempt, message.maxAttempts, message.delay, message.errorType);
            break;
        case 'errorNotification':
            showErrorNotification(message.errorType, message.message, message.status, message.retryable);
            break;
    }
});

// Initialize send button state
updateSendButton();

// Event listeners for message input
messageInput.addEventListener('keydown', (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        if (messageInput.value.trim()) {
            sendMessage();
        }
    }
});

messageInput.addEventListener('input', () => {
    adjustTextareaHeight();
    updateSendButton();
});

// Save state before page unload
window.addEventListener('beforeunload', () => {
    saveUIState();
});

// Save state when visibility changes (tab switch, minimize, etc.)
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        saveUIState();
    }
});

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Start with collapsed state
    const header = document.querySelector('.header');
    header.classList.add('collapsed');
    
    // Update scroll button position on load
    setTimeout(() => {
        updateScrollButtonPosition();
    }, 100);
});

// Make functions global for onclick handlers
window.toggleFilesChanged = toggleFilesChanged;
window.toggleCodeBlock = toggleCodeBlock;
window.copyCodeBlock = copyCodeBlock;
window.forceScrollToBottom = forceScrollToBottom;
window.toggleThreadsPanel = toggleThreadsPanel;
window.requestThreadsList = requestThreadsList;
window.createNewChat = createNewChat;
