// ===== MESSAGE MANAGEMENT =====

// Cache for processed content to improve performance
const contentCache = new Map();

/**
 * Mark a message as final and add action buttons
 */
function markMessageAsFinal(messageId) {
    const message = document.querySelector(`[data-message-id="${messageId}"]`);
    if (!message) {
        console.warn('Message not found for final marking:', messageId);
        return;
    }

    // Remove the needs-actions attribute
    message.removeAttribute('data-needs-actions');

    // Add action buttons if it's an assistant message and doesn't already have them
    if (message.classList.contains('assistant') && !message.querySelector('.message-actions')) {
        const content = message.querySelector('.message-content');
        if (content) {
            const actionsDiv = createMessageActions(messageId, content.textContent || content.innerText);
            message.appendChild(actionsDiv);
        }
    }
}

function processStreamContent(content) {
    // Check cache first for performance
    if (contentCache.has(content)) {
        return contentCache.get(content);
    }

    // Convert markdown to HTML using marked (configured globally)
    let htmlContent = marked.parse(content);

    // Sanitize the HTML
    const sanitizedContent = DOMPurify.sanitize(htmlContent, {
        ALLOWED_TAGS: [
            'p', 'br', 'strong', 'em', 'u', 's', 'code', 'pre',
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            'ul', 'ol', 'li', 'blockquote', 'table', 'thead', 'tbody', 'tr', 'th', 'td',
            'a', 'img', 'hr', 'div', 'span'
        ],
        ALLOWED_ATTR: ['href', 'src', 'alt', 'title', 'class', 'id']
    });

    // Cache the result (limit cache size to prevent memory issues)
    if (contentCache.size > 100) {
        const firstKey = contentCache.keys().next().value;
        contentCache.delete(firstKey);
    }
    contentCache.set(content, sanitizedContent);

    return sanitizedContent;
}

function clearMessages() {
    chatContainer.innerHTML = `
                <div class="welcome-message">
                    <i class="codicon codicon-lightbulb welcome-icon"></i>
                    <div class="welcome-text">
                        <strong>Hello! I'm Augura Coder</strong><br>
                        Your AI programming assistant powered by advanced language models.<br>
                        Ask me anything about coding, and I'll help you out!
                    </div>
                </div>
            `;
}

function restoreSavedMessages(messages) {
    if (!messages || messages.length === 0) {
        return;
    }

    // Remove welcome message if it exists
    const welcomeMsg = chatContainer.querySelector('.welcome-message');
    if (welcomeMsg) {
        welcomeMsg.remove();
    }

    // Use document fragment for better performance
    const fragment = document.createDocumentFragment();

    // Add each saved message to fragment first
    messages.forEach(msg => {
        const messageDiv = createMessageElement(msg.role, msg.content, msg.id, msg.liked, msg.disliked);
        fragment.appendChild(messageDiv);
    });

    // Add all messages at once
    chatContainer.appendChild(fragment);

    // Restore UI state after messages are loaded
    setTimeout(() => {
        restoreUIState();
        updateScrollButtonPosition();
    }, 300);

    // Scroll to bottom if no saved scroll position
    requestAnimationFrame(() => {
        if (!sessionStorage.getItem('augura-ui-state')) {
            scrollToBottomIfEnabled();
        }
    });
}

// Optimized function to create message element without adding to DOM
function createMessageElement(role, content, messageId = null, liked = false, disliked = false) {
    if (role === 'user' && messageId) {
        // Create user message container
        const containerDiv = document.createElement('div');
        containerDiv.className = 'user-message-container';
        containerDiv.setAttribute('data-message-id', messageId);

        // Create message div
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.innerHTML = processStreamContent(content);
        messageDiv.appendChild(contentDiv);

        // Add action buttons for user messages
        const userActionsDiv = createUserMessageActions(messageId, content);

        // Add double-click event for editing
        const editHandler = () => {
            startEditingMessage(messageId, content);
        };
        containerDiv.addEventListener('dblclick', editHandler);
        containerDiv._editHandler = editHandler;

        // Append message and actions to container
        containerDiv.appendChild(messageDiv);
        containerDiv.appendChild(userActionsDiv);

        // Process code blocks and text direction after creating element
        setTimeout(() => {
            processCodeBlocksInElement(contentDiv);
            applySyntaxHighlighting(contentDiv);
            applyTextDirection(messageDiv, content);
        }, 0);

        return containerDiv;
    } else {
        // Create regular message for assistant
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;
        if (messageId) {
            messageDiv.setAttribute('data-message-id', messageId);
        }

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.innerHTML = processStreamContent(content);
        messageDiv.appendChild(contentDiv);

        // Add action buttons for assistant messages
        if (role === 'assistant' && messageId) {
            const actionsDiv = createMessageActions(messageId, content);
            messageDiv.appendChild(actionsDiv);

            // Set reaction states
            if (liked || disliked) {
                setTimeout(() => {
                    updateMessageReaction(messageId, liked, disliked);
                }, 0);
            }
        }

        // Process code blocks and text direction after creating element
        setTimeout(() => {
            processCodeBlocksInElement(contentDiv);
            applySyntaxHighlighting(contentDiv);
            applyTextDirection(messageDiv, content);
        }, 0);

        return messageDiv;
    }
}

function addMessageToChat(role, content, isStreaming = false, messageId = null) {
    if (role === 'user' && !isStreaming && messageId) {
        // Create user message container
        const containerDiv = document.createElement('div');
        containerDiv.className = 'user-message-container';
        containerDiv.setAttribute('data-message-id', messageId);

        // Create message div
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.innerHTML = processStreamContent(content);
        processCodeBlocksInElement(contentDiv);
        applySyntaxHighlighting(contentDiv);
        applyTextDirection(messageDiv, content);
        messageDiv.appendChild(contentDiv);

        // Add action buttons for user messages
        const userActionsDiv = createUserMessageActions(messageId, content);

        // Add double-click event for editing
        const editHandler = () => {
            startEditingMessage(messageId, content);
        };
        containerDiv.addEventListener('dblclick', editHandler);
        containerDiv._editHandler = editHandler;

        // Append message and actions to container
        containerDiv.appendChild(messageDiv);
        containerDiv.appendChild(userActionsDiv);

        chatContainer.appendChild(containerDiv);
        return containerDiv;
    } else {
        // Create regular message for assistant or streaming
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;
        if (messageId) {
            messageDiv.setAttribute('data-message-id', messageId);
        }

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';

        if (isStreaming) {
            contentDiv.innerHTML = '';
        } else {
            contentDiv.innerHTML = processStreamContent(content);
            processCodeBlocksInElement(contentDiv);
            applySyntaxHighlighting(contentDiv);
            applyTextDirection(messageDiv, content);
        }

        messageDiv.appendChild(contentDiv);

        // Add action buttons for assistant messages
        if (role === 'assistant' && !isStreaming) {
            const actionsDiv = createMessageActions(messageId, content);
            messageDiv.appendChild(actionsDiv);
        }

        chatContainer.appendChild(messageDiv);
        return messageDiv;
    }
}

function createMessageActions(messageId, content) {
    const actionsDiv = document.createElement('div');
    actionsDiv.className = 'message-actions';

    // Regenerate button
    const regenerateBtn = document.createElement('button');
    regenerateBtn.className = 'message-action-btn';
    regenerateBtn.innerHTML = '<span class="codicon codicon-refresh"></span>';
    regenerateBtn.title = 'إعادة توليد';
    regenerateBtn.onclick = () => {
        vscode.postMessage({
            type: 'regenerateMessage',
            messageId: messageId
        });
    };

    // Like button
    const likeBtn = document.createElement('button');
    likeBtn.className = 'message-action-btn like-btn';
    likeBtn.innerHTML = '<span class="codicon codicon-thumbsup"></span>';
    likeBtn.title = 'إعجاب';
    likeBtn.onclick = () => {
        const isActive = likeBtn.classList.contains('active');
        vscode.postMessage({
            type: 'likeMessage',
            messageId: messageId,
            liked: !isActive
        });
    };

    // Dislike button
    const dislikeBtn = document.createElement('button');
    dislikeBtn.className = 'message-action-btn dislike-btn';
    dislikeBtn.innerHTML = '<span class="codicon codicon-thumbsdown"></span>';
    dislikeBtn.title = 'عدم إعجاب';
    dislikeBtn.onclick = () => {
        const isActive = dislikeBtn.classList.contains('active');
        vscode.postMessage({
            type: 'dislikeMessage',
            messageId: messageId,
            disliked: !isActive
        });
    };

    // Copy button
    const copyBtn = document.createElement('button');
    copyBtn.className = 'message-action-btn';
    copyBtn.innerHTML = '<span class="codicon codicon-copy"></span>';
    copyBtn.title = 'نسخ';
    copyBtn.onclick = () => {
        vscode.postMessage({
            type: 'copyMessage',
            content: content
        });
    };

    actionsDiv.appendChild(regenerateBtn);
    actionsDiv.appendChild(likeBtn);
    actionsDiv.appendChild(dislikeBtn);
    actionsDiv.appendChild(copyBtn);

    return actionsDiv;
}

function createUserMessageActions(messageId, content) {
    const actionsDiv = document.createElement('div');
    actionsDiv.className = 'user-message-actions';

    // Copy button
    const copyBtn = document.createElement('button');
    copyBtn.className = 'user-action-btn';
    copyBtn.innerHTML = '<span class="codicon codicon-copy"></span>';
    copyBtn.title = 'نسخ';
    copyBtn.onclick = () => {
        vscode.postMessage({
            type: 'copyMessage',
            content: content
        });
    };

    // Edit button
    const editBtn = document.createElement('button');
    editBtn.className = 'user-action-btn edit-btn';
    editBtn.innerHTML = '<span class="codicon codicon-edit"></span>';
    editBtn.title = 'تعديل';
    editBtn.onclick = () => {
        startEditingMessage(messageId, content);
    };

    actionsDiv.appendChild(copyBtn);
    actionsDiv.appendChild(editBtn);

    return actionsDiv;
}

// Message editing functions
function startEditingMessage(messageId, content) {
    const containerElement = document.querySelector(`[data-message-id="${messageId}"]`);
    if (!containerElement) return;

    const messageDiv = containerElement.querySelector('.message');
    const contentDiv = messageDiv ? messageDiv.querySelector('.message-content') : null;
    const actionsDiv = containerElement.querySelector('.user-message-actions');

    if (!contentDiv) return;

    // Hide original content and actions
    contentDiv.style.display = 'none';
    if (actionsDiv) actionsDiv.style.display = 'none';

    // Create edit interface
    const editContainer = document.createElement('div');
    editContainer.className = 'message-edit-container';

    const editInput = document.createElement('textarea');
    editInput.className = 'message-edit-input';
    editInput.value = content;
    editInput.placeholder = 'تعديل الرسالة...';

    const editActions = document.createElement('div');
    editActions.className = 'message-edit-actions';

    const saveBtn = document.createElement('button');
    saveBtn.className = 'edit-action-btn save';
    saveBtn.innerHTML = '<i class="codicon codicon-check"></i> حفظ';
    saveBtn.onclick = () => {
        saveEditedMessage(messageId, editInput.value.trim());
    };

    const cancelBtn = document.createElement('button');
    cancelBtn.className = 'edit-action-btn cancel';
    cancelBtn.innerHTML = '<i class="codicon codicon-close"></i> إلغاء';
    cancelBtn.onclick = () => {
        cancelEditingMessage(messageId);
    };

    editActions.appendChild(cancelBtn);
    editActions.appendChild(saveBtn);
    editContainer.appendChild(editInput);
    editContainer.appendChild(editActions);

    // Insert edit interface
    containerElement.appendChild(editContainer);

    // Focus on input and select all text
    editInput.focus();
    editInput.select();

    // Handle Enter key (Ctrl+Enter to save)
    editInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && e.ctrlKey) {
            e.preventDefault();
            saveEditedMessage(messageId, editInput.value.trim());
        } else if (e.key === 'Escape') {
            e.preventDefault();
            cancelEditingMessage(messageId);
        }
    });
}

function saveEditedMessage(messageId, newContent) {
    if (!newContent) {
        alert('لا يمكن أن تكون الرسالة فارغة');
        return;
    }

    // Send edit message to backend
    vscode.postMessage({
        type: 'editMessage',
        messageId: messageId,
        newContent: newContent
    });
}

function cancelEditingMessage(messageId) {
    const containerElement = document.querySelector(`[data-message-id="${messageId}"]`);
    if (!containerElement) return;

    const messageDiv = containerElement.querySelector('.message');
    const contentDiv = messageDiv ? messageDiv.querySelector('.message-content') : null;
    const actionsDiv = containerElement.querySelector('.user-message-actions');
    const editContainer = containerElement.querySelector('.message-edit-container');

    // Show original content and actions
    if (contentDiv) contentDiv.style.display = 'block';
    if (actionsDiv) actionsDiv.style.display = 'flex';

    // Remove edit interface
    if (editContainer) editContainer.remove();
}

function handleEditMessageComplete(messageId, newContent) {
    const containerElement = document.querySelector(`[data-message-id="${messageId}"]`);
    if (!containerElement) return;

    const messageDiv = containerElement.querySelector('.message');
    const contentDiv = messageDiv ? messageDiv.querySelector('.message-content') : null;
    const actionsDiv = containerElement.querySelector('.user-message-actions');
    const editContainer = containerElement.querySelector('.message-edit-container');

    // Update content
    if (contentDiv) {
        contentDiv.innerHTML = processStreamContent(newContent);
        processCodeBlocksInElement(contentDiv);
        applySyntaxHighlighting(contentDiv);
        applyTextDirection(messageDiv, newContent);
        contentDiv.style.display = 'block';
    }

    // Show actions
    if (actionsDiv) actionsDiv.style.display = 'flex';

    // Remove edit interface
    if (editContainer) editContainer.remove();

    // Update the double-click event with new content
    const newClickHandler = () => {
        startEditingMessage(messageId, newContent);
    };

    // Remove old event listener and add new one
    containerElement.removeEventListener('dblclick', containerElement._editHandler);
    containerElement.addEventListener('dblclick', newClickHandler);
    containerElement._editHandler = newClickHandler;
}

function removeMessage(messageId) {
    const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
    if (messageElement) {
        messageElement.remove();
    }
}

function updateMessageReaction(messageId, liked, disliked) {
    const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
    if (messageElement) {
        const likeBtn = messageElement.querySelector('.like-btn');
        const dislikeBtn = messageElement.querySelector('.dislike-btn');

        if (likeBtn) {
            likeBtn.classList.toggle('active', liked);
        }
        if (dislikeBtn) {
            dislikeBtn.classList.toggle('active', disliked);
        }
    }
}

function sendMessage() {
    const message = messageInput.value.trim();
    if (message) {
        // Force scroll to bottom when sending a new message
        forceScrollToBottom();

        // Show user message immediately
        addMessageToChat('user', message, false, Date.now().toString());

        // Send to extension with agent context
        vscode.postMessage({
            type: 'sendMessage',
            message: message,
            agentMode: true,
            context: {
                currentTask: currentTask,
                timestamp: new Date().toISOString()
            }
        });

        messageInput.value = '';
        adjustTextareaHeight();
        updateSendButton();

        // Show thinking indicator
        showTyping(true);
    }
}

function updateSendButton() {
    const sendBtn = document.querySelector('.send-btn');
    const hasText = messageInput.value.trim().length > 0;
    sendBtn.disabled = !hasText;
}

function clearChat() {
    vscode.postMessage({
        type: 'clearChat'
    });
}

function adjustTextareaHeight() {
    messageInput.style.height = 'auto';
    messageInput.style.height = Math.min(messageInput.scrollHeight, 100) + 'px';
}
