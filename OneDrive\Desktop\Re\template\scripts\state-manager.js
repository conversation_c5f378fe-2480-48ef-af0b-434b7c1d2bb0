// ===== STATE MANAGEMENT =====

// UI State Management Functions
function saveUIState() {
    try {
        // Save scroll position
        uiState.scrollPosition = chatContainer.scrollTop;

        // Save expanded code blocks
        uiState.expandedCodeBlocks.clear();
        document.querySelectorAll('.code-block-content.expanded').forEach(content => {
            const id = content.id.replace('content-', '');
            uiState.expandedCodeBlocks.add(id);
        });

        // Save files changed state
        const filesContent = document.getElementById('filesChangedContent');
        uiState.filesChangedExpanded = filesContent && filesContent.classList.contains('expanded');

        // Store in sessionStorage for persistence
        sessionStorage.setItem('augura-ui-state', JSON.stringify({
            scrollPosition: uiState.scrollPosition,
            expandedCodeBlocks: Array.from(uiState.expandedCodeBlocks),
            filesChangedExpanded: uiState.filesChangedExpanded
        }));
    } catch (error) {
        console.warn('Failed to save UI state:', error);
    }
}

function restoreUIState() {
    try {
        const saved = sessionStorage.getItem('augura-ui-state');
        if (saved) {
            const state = JSON.parse(saved);

            // Restore scroll position
            if (state.scrollPosition) {
                setTimeout(() => {
                    chatContainer.scrollTop = state.scrollPosition;
                }, 100);
            }

            // Restore expanded code blocks
            if (state.expandedCodeBlocks) {
                uiState.expandedCodeBlocks = new Set(state.expandedCodeBlocks);
                state.expandedCodeBlocks.forEach(id => {
                    setTimeout(() => {
                        const content = document.getElementById('content-' + id);
                        const toggle = document.getElementById('toggle-' + id);
                        if (content && toggle) {
                            content.classList.add('expanded');
                            content.classList.remove('collapsed');
                            toggle.classList.remove('collapsed');
                            toggle.classList.remove('codicon-chevron-right');
                            toggle.classList.add('codicon-chevron-down');
                        }
                    }, 200);
                });
            }

            // Restore files changed state
            if (state.filesChangedExpanded) {
                uiState.filesChangedExpanded = true;
                setTimeout(() => {
                    const content = document.getElementById('filesChangedContent');
                    const toggle = document.getElementById('filesToggle');
                    if (content && toggle) {
                        content.classList.add('expanded');
                        content.classList.remove('collapsed');
                        toggle.classList.remove('collapsed');
                        toggle.classList.remove('codicon-chevron-right');
                        toggle.classList.add('codicon-chevron-down');
                    }
                }, 200);
            }

            // Update scroll button position after restoring files changed state
            setTimeout(() => {
                updateScrollButtonPosition();
            }, 250);
        }
    } catch (error) {
        console.warn('Failed to restore UI state:', error);
    }
}
