// ===== STREAMING MANAGEMENT =====

function startStreamingMessage(role, messageId = null) {
    // Remove welcome message if it exists
    const welcomeMsg = chatContainer.querySelector('.welcome-message');
    if (welcomeMsg) {
        welcomeMsg.remove();
    }

    // Create streaming message container
    currentStreamingMessage = document.createElement('div');
    currentStreamingMessage.className = `message ${role}`;

    // Set message ID if provided
    if (messageId) {
        currentStreamingMessage.setAttribute('data-message-id', messageId);
    }

    const content = document.createElement('div');
    content.className = 'message-content';
    content.innerHTML = '';

    currentStreamingMessage.appendChild(content);

    // Only add to DOM if it's a user message or if we have content
    if (role === 'user') {
        chatContainer.appendChild(currentStreamingMessage);
    }

    // Reset streaming state
    isInThinkingMode = false;
    responseBuffer = '';

    // Show thinking indicator for assistant messages
    if (role === 'assistant') {
        showTyping(true);
    }

    scrollToBottomIfEnabled();
}

function addStreamChunk(chunk) {
    if (!currentStreamingMessage) return;

    const content = currentStreamingMessage.querySelector('.message-content');
    responseBuffer += chunk;

    // Check for thinking tags
    if (responseBuffer.includes('<think>')) {
        isInThinkingMode = true;
        showTyping(true);
    }

    if (isInThinkingMode) {
        // We're in thinking mode, don't display content yet
        if (responseBuffer.includes('</think>')) {
            // End of thinking, extract the response after </think>
            const thinkEndIndex = responseBuffer.indexOf('</think>') + 8;
            const actualResponse = responseBuffer.substring(thinkEndIndex);

            isInThinkingMode = false;
            showTyping(false);

            // Add message to DOM when we have actual content
            if (!currentStreamingMessage.parentNode && actualResponse.trim()) {
                chatContainer.appendChild(currentStreamingMessage);
            }

            // Display the actual response with performance optimization
            if (actualResponse.trim()) {
                // Use requestAnimationFrame for better performance during streaming
                requestAnimationFrame(() => {
                    content.innerHTML = processStreamContent(actualResponse);
                    processCodeBlocksInElement(content);
                    applySyntaxHighlighting(content);
                    applyTextDirection(currentStreamingMessage, actualResponse);
                });
            }
        }
        // Don't display anything while thinking
    } else {
        // Normal streaming mode - add to DOM if not already there
        if (!currentStreamingMessage.parentNode && responseBuffer.trim()) {
            chatContainer.appendChild(currentStreamingMessage);
            showTyping(false);
        }

        if (responseBuffer.trim()) {
            content.innerHTML = processStreamContent(responseBuffer);
            processCodeBlocksInElement(content);
            applySyntaxHighlighting(content);
            applyTextDirection(currentStreamingMessage, responseBuffer);
        }
    }

    scrollToBottomIfEnabled();
}

function endStreamingMessage() {
    if (!currentStreamingMessage) return;

    const content = currentStreamingMessage.querySelector('.message-content');

    // Final processing
    if (!isInThinkingMode && responseBuffer.trim()) {
        // Add to DOM if not already there
        if (!currentStreamingMessage.parentNode) {
            chatContainer.appendChild(currentStreamingMessage);
        }

        // Remove any remaining <think> content
        let finalContent = responseBuffer;
        if (finalContent.includes('<think>') && finalContent.includes('</think>')) {
            const thinkStart = finalContent.indexOf('<think>');
            const thinkEnd = finalContent.indexOf('</think>') + 8;
            finalContent = finalContent.substring(0, thinkStart) + finalContent.substring(thinkEnd);
        }

        content.innerHTML = processStreamContent(finalContent);
        processCodeBlocksInElement(content);
        applySyntaxHighlighting(content);
        applyTextDirection(currentStreamingMessage, finalContent);

        // Don't add action buttons automatically - they will be added only for final messages
        // Mark this message as potentially needing actions later
        if (currentStreamingMessage.classList.contains('assistant')) {
            currentStreamingMessage.setAttribute('data-needs-actions', 'true');
        }

        // Add action buttons for user messages
        if (currentStreamingMessage.classList.contains('user')) {
            const messageId = currentStreamingMessage.getAttribute('data-message-id');
            if (messageId) {
                // Create container for user message
                const containerDiv = document.createElement('div');
                containerDiv.className = 'user-message-container';
                containerDiv.setAttribute('data-message-id', messageId);

                // Move the current message into the container
                currentStreamingMessage.parentNode.insertBefore(containerDiv, currentStreamingMessage);
                containerDiv.appendChild(currentStreamingMessage);

                // Add action buttons
                const userActionsDiv = createUserMessageActions(messageId, finalContent);
                containerDiv.appendChild(userActionsDiv);

                // Add double-click event for editing
                const editHandler = () => {
                    startEditingMessage(messageId, finalContent);
                };
                containerDiv.addEventListener('dblclick', editHandler);
                containerDiv._editHandler = editHandler;

                // Remove message ID from inner message div
                currentStreamingMessage.removeAttribute('data-message-id');
            }
        }
    }

    showTyping(false);
    currentStreamingMessage = null;
    scrollToBottomIfEnabled();
}

function showTyping(isTyping) {
    typingIndicator.style.display = isTyping ? 'block' : 'none';
    if (isTyping) {
        // Move typing indicator to end of chat
        chatContainer.appendChild(typingIndicator);
        scrollToBottomIfEnabled();
    }
}
