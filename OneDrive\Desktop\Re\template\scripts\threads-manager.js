// ===== THREADS MANAGEMENT =====

// Threads management functions
function toggleThreadsPanel() {
    const header = document.querySelector('.header');
    const threadsContainer = document.querySelector('.threads-container');
    const toggleBtn = document.getElementById('threadsToggleBtn');
    const chevron = document.getElementById('threadsChevron');

    threadsPanelOpen = !threadsPanelOpen;

    if (threadsPanelOpen) {
        // Show threads panel
        header.classList.remove('collapsed');
        header.classList.add('expanded');
        threadsContainer.classList.add('show');
        toggleBtn.classList.add('active');
        chevron.classList.remove('codicon-chevron-right');
        chevron.classList.add('codicon-chevron-down');

        // Request threads list from extension
        vscode.postMessage({ type: 'requestThreadsList' });
    } else {
        // Hide threads panel
        header.classList.remove('expanded');
        header.classList.add('collapsed');
        threadsContainer.classList.remove('show');
        toggleBtn.classList.remove('active');
        chevron.classList.remove('codicon-chevron-down');
        chevron.classList.add('codicon-chevron-right');
    }
}

function requestThreadsList() {
    // Request threads list from extension
    vscode.postMessage({ type: 'requestThreadsList' });
}

function updateThreadsList(threads) {
    currentThreads = threads;
    const todayContainer = document.getElementById('todayThreads');
    const weekContainer = document.getElementById('weekThreads');

    // Clear existing content
    todayContainer.innerHTML = '';
    weekContainer.innerHTML = '';

    if (!threads || threads.length === 0) {
        todayContainer.innerHTML = '<div class="no-threads">لا توجد محادثات</div>';
        return;
    }

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    threads.forEach(thread => {
        const threadDate = new Date(thread.lastActivity);
        const threadDay = new Date(threadDate.getFullYear(), threadDate.getMonth(), threadDate.getDate());

        const threadElement = createThreadElement(thread);

        if (threadDay.getTime() === today.getTime()) {
            todayContainer.appendChild(threadElement);
        } else if (threadDate >= weekAgo) {
            weekContainer.appendChild(threadElement);
        }
    });

    // Hide sections if empty
    if (todayContainer.children.length === 0) {
        todayContainer.innerHTML = '<div class="no-threads">لا توجد محادثات اليوم</div>';
    }
    if (weekContainer.children.length === 0) {
        weekContainer.innerHTML = '<div class="no-threads">لا توجد محادثات هذا الأسبوع</div>';
    }
}

function createThreadElement(thread) {
    const threadItem = document.createElement('div');
    threadItem.className = `thread-item ${thread.isActive ? 'active' : ''}`;
    threadItem.onclick = () => switchToThread(thread.id);

    // Add accessibility attributes
    threadItem.setAttribute('role', 'button');
    threadItem.setAttribute('tabindex', '0');
    threadItem.setAttribute('aria-label', `محادثة: ${thread.title}. آخر نشاط: ${new Date(thread.lastActivity).toLocaleString('ar-SA')}`);

    // Add keyboard navigation
    threadItem.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            switchToThread(thread.id);
        }
    });

    // Format time
    const lastActivity = new Date(thread.lastActivity);
    const now = new Date();
    const diffMs = now - lastActivity;
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    let timeText;
    if (diffMins < 1) {
        timeText = 'الآن';
    } else if (diffMins < 60) {
        timeText = `${diffMins} د`;
    } else if (diffHours < 24) {
        timeText = `${diffHours} س`;
    } else if (diffDays < 7) {
        timeText = `${diffDays} ي`;
    } else {
        timeText = lastActivity.toLocaleDateString('ar-SA', { month: 'short', day: 'numeric' });
    }

    // Get message count
    const messageCount = thread.messageCount || 0;

    threadItem.innerHTML = `
        <div class="thread-info">
            <div class="thread-title">${thread.title}</div>
            <div class="thread-preview">${thread.preview}</div>
            <div class="thread-meta">
                <span class="thread-time">${timeText}</span>
                ${messageCount > 0 ? `<span class="thread-message-count">${messageCount}</span>` : ''}
            </div>
        </div>
        <div class="thread-actions">
            <button class="thread-action-btn delete" onclick="deleteThread('${thread.id}', event)" title="حذف المحادثة" aria-label="حذف محادثة ${thread.title}">
                <i class="codicon codicon-trash"></i>
            </button>
        </div>
    `;

    return threadItem;
}

function switchToThread(threadId) {
    vscode.postMessage({
        type: 'switchThread',
        threadId: threadId
    });
}

function deleteThread(threadId, event) {
    event.stopPropagation(); // Prevent thread switch
    vscode.postMessage({
        type: 'deleteThread',
        threadId: threadId
    });
}

function createNewChat() {
    vscode.postMessage({
        type: 'createNewThread'
    });
}

function handleThreadSwitch(threadId) {
    // Update UI to reflect thread switch
    currentThreads.forEach(thread => {
        thread.isActive = thread.id === threadId;
    });
    updateThreadsList(currentThreads);
}
