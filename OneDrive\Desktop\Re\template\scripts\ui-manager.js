// ===== UI MANAGEMENT & SCROLL CONTROL =====

// Scroll management - detect user scrolling
chatContainer.addEventListener('scroll', () => {
    if (scrollTimeout) {
        clearTimeout(scrollTimeout);
    }

    userIsScrolling = true;

    // Check if user scrolled to bottom
    const isAtBottom = chatContainer.scrollTop + chatContainer.clientHeight >= chatContainer.scrollHeight - 10;
    autoScrollEnabled = isAtBottom;

    // Show/hide scroll to bottom button
    if (isAtBottom) {
        scrollToBottomBtn.classList.remove('visible');
    } else {
        scrollToBottomBtn.classList.add('visible');
    }

    // Reset user scrolling flag after a delay
    scrollTimeout = setTimeout(() => {
        userIsScrolling = false;
        // Save scroll position
        uiState.scrollPosition = chatContainer.scrollTop;
    }, 150);
});

// Detect wheel/touch scrolling to disable auto-scroll
chatContainer.addEventListener('wheel', () => {
    userIsScrolling = true;
    autoScrollEnabled = false;
});

chatContainer.addEventListener('touchstart', () => {
    userIsScrolling = true;
    autoScrollEnabled = false;
});

// Smart scroll function that respects user interaction
function scrollToBottomIfEnabled() {
    if (autoScrollEnabled && !userIsScrolling) {
        // Add small delay for more natural scrolling
        setTimeout(() => {
            if (autoScrollEnabled && !userIsScrolling) {
                chatContainer.scrollTo({
                    top: chatContainer.scrollHeight,
                    behavior: 'smooth'
                });
            }
        }, 50);
    }
}

// Function to force scroll to bottom (for new messages)
function forceScrollToBottom() {
    autoScrollEnabled = true;
    userIsScrolling = false;

    // Add visual feedback to button
    scrollToBottomBtn.style.transform = 'translateX(-50%) scale(0.9)';

    // Smooth scroll to bottom
    chatContainer.scrollTo({
        top: chatContainer.scrollHeight,
        behavior: 'smooth'
    });

    // Hide button and reset transform after scroll
    setTimeout(() => {
        scrollToBottomBtn.classList.remove('visible');
        scrollToBottomBtn.style.transform = 'translateX(-50%) scale(1)';
    }, 300);
}

// Function to update scroll button position based on files changed panel
function updateScrollButtonPosition() {
    const filesSection = document.getElementById('filesChangedSection');
    const isExpanded = filesSection && filesSection.querySelector('.files-changed-content').classList.contains('expanded');

    if (isExpanded) {
        // Files panel is open - move button up
        scrollToBottomBtn.style.bottom = '210px'; // Adjust based on files panel height
    } else {
        // Files panel is closed - normal position
        scrollToBottomBtn.style.bottom = '140px';
    }
}

// Files changed functionality
function toggleFilesChanged(event) {
    // Prevent event bubbling if called from icon click
    if (event) {
        event.stopPropagation();
    }

    const content = document.getElementById('filesChangedContent');
    const toggle = document.getElementById('filesToggle');

    // Check if it's in default collapsed state (no classes) or explicitly collapsed
    const isCollapsed = !content.classList.contains('expanded');

    if (isCollapsed) {
        // Expand - show content, arrow points down
        content.classList.add('expanded');
        content.classList.remove('collapsed');
        toggle.classList.remove('collapsed');
        // Change icon to chevron-down (pointing down when expanded)
        toggle.classList.remove('codicon-chevron-right');
        toggle.classList.add('codicon-chevron-down');
        // Save state
        uiState.filesChangedExpanded = true;
    } else {
        // Collapse - hide content, arrow points right
        content.classList.remove('expanded');
        content.classList.add('collapsed');
        toggle.classList.add('collapsed');
        // Change icon to chevron-right (pointing right when collapsed)
        toggle.classList.remove('codicon-chevron-down');
        toggle.classList.add('codicon-chevron-right');
        // Save state
        uiState.filesChangedExpanded = false;
    }

    // Update scroll to bottom button position
    updateScrollButtonPosition();

    // Save state immediately
    saveUIState();
}
