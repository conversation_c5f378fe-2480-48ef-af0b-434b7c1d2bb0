// ===== UTILITY FUNCTIONS =====

// Text direction detection
function detectTextDirection(text) {
    // Remove code blocks from direction detection
    const textWithoutCode = text.replace(/```[\\s\\S]*?```/g, '')
        .replace(/`[^`]+`/g, '')
        .replace(/<pre[\\s\\S]*?<\/pre>/gi, '')
        .replace(/<code[\\s\\S]*?<\/code>/gi, '');

    // Use bidi-js for direction detection
    if (typeof Bidi !== 'undefined') {
        try {
            const bidiText = new Bidi(textWithoutCode, { dir: 'auto' });
            return bidiText.dir;
        } catch (e) {
            // Fallback to simple detection
            return detectSimpleDirection(textWithoutCode);
        }
    }

    return detectSimpleDirection(textWithoutCode);
}

function detectSimpleDirection(text) {
    // Simple RTL detection for Arabic, Hebrew, Persian, etc.
    const rtlChars = /[\u0590-\u05FF\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
    const ltrChars = /[A-Za-z]/;

    const rtlCount = (text.match(rtlChars) || []).length;
    const ltrCount = (text.match(ltrChars) || []).length;

    if (rtlCount > ltrCount) return 'rtl';
    if (ltrCount > rtlCount) return 'ltr';
    return 'auto';
}

function applyTextDirection(element, content) {
    const direction = detectTextDirection(content);
    element.setAttribute('dir', direction);
}

// Get file icon based on extension
function getFileIcon(extension) {
    const iconMap = {
        'js': 'codicon-symbol-method',
        'ts': 'codicon-symbol-method',
        'html': 'codicon-symbol-structure',
        'css': 'codicon-symbol-color',
        'json': 'codicon-symbol-object',
        'md': 'codicon-symbol-text',
        'py': 'codicon-symbol-method',
        'java': 'codicon-symbol-class',
        'cpp': 'codicon-symbol-method',
        'c': 'codicon-symbol-method',
        'php': 'codicon-symbol-method',
        'rb': 'codicon-symbol-method',
        'go': 'codicon-symbol-method',
        'rs': 'codicon-symbol-method',
        'xml': 'codicon-symbol-structure',
        'yml': 'codicon-symbol-object',
        'yaml': 'codicon-symbol-object',
        'txt': 'codicon-symbol-text',
        'log': 'codicon-symbol-text'
    };

    return iconMap[extension] || 'codicon-symbol-file';
}
