/* Message action buttons */
.message-actions {
    display: flex;
    gap: 6px;
    margin-top: 8px;
    opacity: 0;

    align-items: center;
    flex-wrap: nowrap;
}

.message.assistant:hover .message-actions {
    opacity: 1;
}

/* User message action buttons */
.user-message-actions {
    display: flex;
    gap: 6px;
    opacity: 0;

    align-items: center;
    flex-wrap: nowrap;
    justify-content: flex-end;
    margin-top: 4px;
}

.user-message-container:hover .user-message-actions {
    opacity: 1;
}

.message-action-btn {
    background: transparent;
    border: 1px solid var(--vscode-panel-border);
    color: var(--vscode-foreground);
    padding: 0;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    justify-content: center;

    width: 22px;
    height: 22px;
    min-width: 22px;
    min-height: 22px;
    max-width: 22px;
    max-height: 22px;
    flex-shrink: 0;
    flex-grow: 0;
    box-sizing: border-box;
}

.message-action-btn:hover {
    background: var(--vscode-list-hoverBackground);
    border-color: var(--vscode-focusBorder);
}

.message-action-btn.active {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border-color: var(--vscode-button-background);
}

.message-action-btn.active.like-btn {
    background: var(--vscode-testing-iconPassed);
    border-color: var(--vscode-testing-iconPassed);
    color: white;
}

.message-action-btn.active.dislike-btn {
    background: var(--vscode-testing-iconFailed);
    border-color: var(--vscode-testing-iconFailed);
    color: white;
}

.message-action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.message-action-btn .codicon {
    font-size: 11px;
    line-height: 1;
    display: block;
}

/* User message action button styles */
.user-action-btn {
    background: transparent;
    border: 1px solid var(--vscode-panel-border);
    color: var(--vscode-foreground);
    padding: 0;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    justify-content: center;

    width: 22px;
    height: 22px;
    min-width: 22px;
    min-height: 22px;
    max-width: 22px;
    max-height: 22px;
    flex-shrink: 0;
    flex-grow: 0;
    box-sizing: border-box;
}

.user-action-btn:hover {
    background: var(--vscode-list-hoverBackground);
    border-color: var(--vscode-focusBorder);
}

.user-action-btn.edit-btn:hover {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border-color: var(--vscode-button-background);
}

.user-action-btn .codicon {
    font-size: 11px;
    line-height: 1;
    display: block;
}

/* Retry countdown styles */
.retry-countdown {
    background: var(--vscode-notifications-background);
    border: 1px solid var(--vscode-notifications-border);
    border-radius: 8px;
    padding: 12px;
    margin: 10px 0;
    text-align: center;
    color: var(--vscode-notifications-foreground);
}

.retry-countdown .countdown-text {
    font-size: 14px;
    margin-bottom: 8px;
}

.retry-countdown .countdown-timer {
    font-size: 18px;
    font-weight: bold;
    color: var(--vscode-errorForeground);
}

.retry-thinking {
    background: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    border-radius: 8px;
    padding: 12px;
    margin: 10px 0;
    text-align: center;
    color: var(--vscode-foreground);
}
