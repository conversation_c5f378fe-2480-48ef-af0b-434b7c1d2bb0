body {
    font-family: var(--vscode-font-family);
    font-size: var(--vscode-font-size);
    color: var(--vscode-foreground);
    background-color: var(--vscode-sideBar-background);
    margin: 0;
    padding: 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Syntax highlighting color variables */
:root {
    --syntax-comment: #6A9955;
    --syntax-punctuation: #D4D4D4;
    --syntax-property: #B5CEA8;
    --syntax-string: #CE9178;
    --syntax-operator: #D4D4D4;
    --syntax-keyword: #569CD6;
    --syntax-function: #DCDCAA;
    --syntax-variable: #9CDCFE;
    --syntax-namespace: #4EC9B0;
    --syntax-escape: #D7BA7D;
    --syntax-tag: #92C5F8;
    --syntax-interpolation: #FF8C94;
    --syntax-added: #89d185;
    --syntax-deleted: #f85149;
}

.welcome-message {
    text-align: center;
    color: var(--vscode-descriptionForeground);
    padding: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    border: 1px solid var(--vscode-input-border);
    border-radius: 8px;
    margin: 16px;
    background-color: var(--vscode-editor-background);
}

.welcome-icon {
    font-size: 32px;
    color: var(--vscode-textLink-foreground);
}

.welcome-text {
    line-height: 1.5;
}

.welcome-text strong {
    color: var(--vscode-foreground);
    font-size: 16px;
}


.btn-icon {
    font-size: 14px;
}

/* Codicon styles */
.codicon {
    font-family: 'codicon';
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-decoration: inherit;
    text-transform: none;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

@font-face {
    font-family: 'codicon';
    src: url('https://cdn.jsdelivr.net/npm/@vscode/codicons@0.0.35/dist/codicon.ttf') format('truetype');
}

/* Scroll to bottom button */
.scroll-to-bottom {
    position: absolute;
    bottom: 140px;
    left: 50%;
    transform: translateX(-50%);
    width: 25px;
    height: 25px;
    border-radius: 50%;
    border: 2px solid var(--vscode-panel-border);
    background-color: var(--vscode-sideBar-background);
    color: var(--vscode-foreground);
    cursor: pointer;
    display: none;
    align-items: center;
    justify-content: center;

    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    transition: bottom 0.1s ease;
}

.scroll-to-bottom.visible {
    display: flex;
}

.scroll-to-bottom:hover {
    border-color: var(--vscode-focusBorder);
    background-color: var(--vscode-list-hoverBackground);
}

.scroll-to-bottom:active {
    transform: translateX(-50%) scale(0.98);
}
