.chat-container {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    scroll-behavior: smooth;
}

.message {
    padding: 8px 12px;
    border-radius: 8px;
    word-wrap: break-word;
}

.message.user {
    padding-top: 0.3px;
    padding-bottom: 0.3px;
    max-width: 90%;
    align-self: flex-end;
    background-color: rgba(18, 132, 254, 0.2);
    color: rgb(55, 162, 255);
}

/* User message container with actions */
.user-message-container {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
    max-width: 90%;
    align-self: flex-end;
}

.message.assistant {
    padding: 8px 12px;
    max-width: calc(100% - 24px);
    width: calc(100% - 24px);
    align-self: flex-start;
    background-color: rgba(0, 0, 0, 0.0);
    box-sizing: border-box;
}

/* Text direction support */
.message[dir="rtl"] {
    direction: rtl;
    text-align: right;
}

.message[dir="ltr"] {
    direction: ltr;
    text-align: left;
}

.message[dir="auto"] {
    direction: auto;
    text-align: start;
}



.message pre {
    background-color: transparent;
    border: none;
    border-radius: 0;
    padding: 12px;
    overflow-x: auto;
    margin: 0;
    font-family: var(--vscode-editor-font-family);
    font-size: 13px;
    line-height: 1.4;
}




.message h1,
.message h2,
.message h3,
.message h4,
.message h5,
.message h6 {
    color: var(--vscode-foreground);
    margin: 16px 0 8px 0;
    font-weight: bold;
    line-height: 1.3;
}

.message h1 {
    font-size: 1.6em;
    border-bottom: 2px solid var(--vscode-textLink-foreground);
    padding-bottom: 4px;
}

.message h2 {
    font-size: 1.4em;
    border-bottom: 1px solid var(--vscode-input-border);
    padding-bottom: 2px;
}

.message h3 {
    font-size: 1.2em;
    color: var(--vscode-textLink-foreground);
}

.message h4 {
    font-size: 1.1em;
}

.message h5 {
    font-size: 1.05em;
}

.message h6 {
    font-size: 1em;
    font-style: italic;
}

.message ul,
.message ol {
    margin: 8px 0;
    padding-left: 24px;
}

.message li {
    margin: 4px 0;
    line-height: 1.5;
}

.message ul li {
    list-style-type: disc;
}

.message ol li {
    list-style-type: decimal;
}

.message ul ul li {
    list-style-type: circle;
}

.message ul ul ul li {
    list-style-type: square;
}

.message blockquote {
    border-left: 4px solid var(--vscode-textBlockQuote-border);
    background-color: var(--vscode-textBlockQuote-background);
    margin: 12px 0;
    padding: 12px 16px;
    font-style: italic;
    border-radius: 0 6px 6px 0;
    position: relative;
}

.message blockquote::before {
    content: '"';
    font-size: 2em;
    color: var(--vscode-textBlockQuote-border);
    position: absolute;
    top: -5px;
    left: 8px;
    font-family: serif;
}

.message blockquote p {
    margin: 0;
    padding-left: 20px;
}

.table-wrapper {
    overflow-x: auto;
    margin: 12px 0;
    border: 1px solid var(--vscode-input-border);
    border-radius: 6px;
}

.message table {
    border-collapse: collapse;
    width: 100%;
    min-width: 100%;
    border: none;
}

.message th,
.message td {
    border: 1px solid var(--vscode-input-border);
    padding: 8px 12px;
    text-align: left;
    word-wrap: break-word;
    overflow-wrap: break-word;
    min-width: 100px;
    max-width: 300px;
}

.message th {
    background-color: var(--vscode-input-background);
    font-weight: bold;
    color: var(--vscode-foreground);
}

.message tr:nth-child(even) {
    background-color: var(--vscode-editor-background);
}

.message tr:hover {
    background-color: var(--vscode-list-hoverBackground);
}

.message a {
    color: var(--vscode-textLink-foreground);
    text-decoration: none;
    border-bottom: 1px dotted var(--vscode-textLink-foreground);

}

.message a:hover {
    color: var(--vscode-textLink-activeForeground);
    text-decoration: none;
    border-bottom: 1px solid var(--vscode-textLink-activeForeground);
}

.message img {
    max-width: 100%;
    height: auto;
    border-radius: 6px;
    margin: 8px 0;
    border: 1px solid var(--vscode-input-border);
}

.message hr {
    border: none;
    border-top: 1px solid var(--vscode-input-border);
    margin: 16px 0;
}

.message p {
    margin: 8px 0;
    line-height: 1.5;
}

.message strong {
    font-weight: bold;
}

.message em {
    font-style: italic;
}

.typing-indicator {
    align-self: flex-start;
    padding: 8px 12px;
    background-color: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    border-radius: 8px;
    display: none;
    max-width: 90%;
    margin: 10px 0;
}

.typing-dots {
    display: inline-block;
}

.typing-dots::after {
    content: '...';
    animation: typing 1.5s infinite;
}

@keyframes typing {
    0%, 60% {
        content: '...';
    }
    20% {
        content: '.';
    }
    40% {
        content: '..';
    }
}

/* Enhanced retry notification styles */
.retry-notification {
    align-self: center;
    margin: 16px 0;
    padding: 16px;
    background: linear-gradient(135deg,
        rgba(255, 193, 7, 0.1) 0%,
        rgba(255, 152, 0, 0.1) 100%);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 12px;
    max-width: 400px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    animation: slideIn 0.3s ease-out;
}

.retry-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.retry-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--vscode-foreground);
}

.retry-header .codicon {
    color: #ff9800;
    font-size: 16px;
}

.retry-title {
    font-size: 14px;
    text-transform: capitalize;
}

.retry-message {
    font-size: 13px;
    color: var(--vscode-descriptionForeground);
    text-align: center;
}

.countdown-timer {
    font-weight: bold;
    color: #ff9800;
    font-size: 14px;
}

.retry-progress {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.retry-attempt {
    font-size: 12px;
    color: var(--vscode-descriptionForeground);
    text-align: center;
}

.retry-progress-bar {
    height: 4px;
    background-color: rgba(255, 152, 0, 0.2);
    border-radius: 2px;
    overflow: hidden;
}

.retry-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff9800, #ffc107);
    border-radius: 2px;
    transition: width 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Legacy retry countdown styles for compatibility */
.retry-countdown {
    align-self: center;
    margin: 16px 0;
    padding: 12px 16px;
    background-color: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 8px;
    text-align: center;
    max-width: 300px;
    animation: slideIn 0.3s ease-out;
}

.countdown-text {
    font-size: 13px;
    color: var(--vscode-descriptionForeground);
    margin-bottom: 4px;
}

.countdown-timer {
    font-size: 16px;
    font-weight: bold;
    color: #ff9800;
}

/* Error notification styles */
.error-notification {
    align-self: center;
    margin: 16px 0;
    padding: 16px;
    background: linear-gradient(135deg,
        rgba(244, 67, 54, 0.1) 0%,
        rgba(211, 47, 47, 0.1) 100%);
    border: 1px solid rgba(244, 67, 54, 0.3);
    border-radius: 12px;
    max-width: 500px;
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.1);
    animation: slideIn 0.3s ease-out;
}

.error-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.error-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--vscode-foreground);
}

.error-header .codicon {
    color: #f44336;
    font-size: 16px;
}

.error-title {
    font-size: 14px;
    color: #f44336;
}

.error-message {
    font-size: 13px;
    color: var(--vscode-foreground);
    line-height: 1.5;
    padding: 8px 12px;
    background-color: rgba(244, 67, 54, 0.05);
    border-radius: 6px;
    border-left: 3px solid #f44336;
    white-space: pre-wrap;
}

.error-footer {
    display: flex;
    justify-content: center;
}

.error-hint {
    font-size: 12px;
    color: var(--vscode-descriptionForeground);
    font-style: italic;
    text-align: center;
}


