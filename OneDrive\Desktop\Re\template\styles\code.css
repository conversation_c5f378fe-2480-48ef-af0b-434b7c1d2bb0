/* Keep code blocks always LTR */
.message[dir="rtl"] .code-block,
.message[dir="rtl"] pre,
.message[dir="rtl"] code,
.message[dir="auto"] .code-block,
.message[dir="auto"] pre,
.message[dir="auto"] code {
    direction: ltr;
    text-align: left;
}

/* Keep tables neutral */
.message[dir="rtl"] .table-wrapper,
.message[dir="auto"] .table-wrapper {
    direction: ltr;
}


.code-block {
    background-color: var(--vscode-textCodeBlock-background);
    border: 1px solid var(--vscode-panel-border);
    border-radius: 6px;
    margin: 8px 0;
    overflow: hidden;
}

.code-block-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background-color: var(--vscode-sideBar-background);
    border-bottom: 1px solid var(--vscode-panel-border);
    font-size: 12px;
    color: var(--vscode-descriptionForeground);
}

.code-block-title {
    display: flex;
    align-items: center;
    gap: 8px;
}

.code-block-language {
    font-weight: 500;
    color: var(--vscode-foreground);
}

.code-block-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.code-block-toggle {
    font-size: 14px;
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 16px;
    min-height: 16px;
}

.code-block-toggle:hover {
    background-color: var(--vscode-toolbar-hoverBackground);
}

.code-block-copy {
    background: none;
    border: none;
    color: var(--vscode-descriptionForeground);
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    font-size: 14px;
    opacity: 0.7;
    transition: all 0.2s ease;
}

.code-block-copy:hover {
    background-color: var(--vscode-toolbar-hoverBackground);
    color: var(--vscode-foreground);
    opacity: 1;
}

.code-block-content {
    transition: max-height 0.3s ease;
    overflow: hidden;
    max-height: 0; /* Default collapsed state */
}

.code-block-content.expanded {
    max-height: none; /* Expanded state */
}

.code-block-content.collapsed {
    max-height: 0;
}

.message pre {
    background-color: transparent;
    border: none;
    margin: 0;
    padding: 12px;
    overflow-x: auto;
    font-family: var(--vscode-editor-font-family, 'Consolas', 'Monaco', 'Courier New', monospace);
    font-size: 13px;
    line-height: 1.4;
    border-radius: 0;
}

.message code {
    font-family: var(--vscode-editor-font-family, 'Consolas', 'Monaco', 'Courier New', monospace);
    font-size: 13px;
    line-height: 1.4;
}


/* Ensure syntax highlighting is visible in all VS Code themes */
.code-block-content code,
.message code {
    color: var(--vscode-editor-foreground, #D4D4D4);
}

/* Override Prism.js default background to match VS Code */
.code-block-content pre[class*="language-"],
.code-block-content code[class*="language-"] {
    background: transparent !important;
    color: var(--vscode-editor-foreground, #D4D4D4) !important;
}

/* Ensure all Prism.js tokens are visible */
.code-block-content .token,
.message code .token {
    background: none !important;
}

/* Loading indicator for code blocks */
.code-block-content code.prism-loading::after,
.message code.prism-loading::after {
    content: " (loading...)";
    color: var(--vscode-descriptionForeground);
    font-style: italic;
    opacity: 0.7;
}

/* Ensure syntax highlighting works with all themes */
.code-block-content,
.message pre,
.message code {
    font-family: var(--vscode-editor-font-family, 'Consolas', 'Monaco', 'Courier New', monospace) !important;
}



/* Responsive code block styling */
@media (max-width: 768px) {
    .code-block-content pre {
        font-size: 12px;
        padding: 8px;
    }

    .message code {
        font-size: 12px;
        padding: 1px 4px;
    }
}
