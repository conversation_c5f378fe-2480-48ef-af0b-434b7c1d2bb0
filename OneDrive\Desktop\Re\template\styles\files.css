/* Files Changed Section - VS Code Style */
.files-changed-section {
    margin: 8px;
    border: 1px solid var(--vscode-panel-border);
    border-radius: 3px;
    background-color: var(--vscode-sideBar-background);
    overflow: hidden;
}

.files-changed-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 8px;
    background-color: var(--vscode-sideBar-background);
    border-bottom: 1px solid var(--vscode-panel-border);
    font-size: 13px;
    color: var(--vscode-foreground);
    min-height: 28px;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 6px;
    flex: 1;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.files-changed-toggle {
    font-size: 11px;
    color: var(--vscode-foreground);
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 16px;
    min-height: 16px;
}

.files-changed-toggle:hover {
    background-color: var(--vscode-toolbar-hoverBackground);
    color: var(--vscode-foreground);
}

.files-changed-icon {
    font-size: 13px;
    color: var(--vscode-foreground);
}

.files-changed-text {
    font-size: 13px;
    color: var(--vscode-foreground);
    font-weight: 400;
}

.files-changed-stats {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-family: var(--vscode-editor-font-family, 'Consolas', monospace);
}

.stats-added {
    color: #89d185;
    font-weight: 400;
}

.stats-deleted {
    color: #f85149;
    font-weight: 400;
}

.files-changed-content {
    max-height: 200px;
    overflow-y: auto;
    background-color: var(--vscode-editor-background);
    display: none;
}

.files-changed-content.expanded {
    display: block;
}

.files-changed-content.collapsed {
    display: none;
}

.files-changed-actions {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-shrink: 0;
}

.files-changed-actions .action-btn {
    background: none;
    border: 1px solid transparent;
    padding: 4px 8px;
    cursor: pointer;
    font-size: 11px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3px;
    border-radius: 3px;

    color: var(--vscode-foreground);
    white-space: nowrap;
    min-width: fit-content;
    height: 24px;
    box-sizing: border-box;
}

.files-changed-actions .action-btn:hover {
    background: var(--vscode-toolbar-hoverBackground);
}

.files-changed-actions .action-btn.discard {
    color: var(--vscode-foreground);
    border: 1px solid transparent;
}

.files-changed-actions .action-btn.discard:hover {
    background: var(--vscode-errorBackground);
    color: var(--vscode-errorForeground);
    border-color: var(--vscode-errorForeground);
}

.files-changed-actions .action-btn.keep {
    color: var(--vscode-foreground);
    border: 1px solid transparent;
}

.files-changed-actions .action-btn.keep:hover {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border-color: var(--vscode-button-background);
}

.action-text {
    font-size: 10px;
    line-height: 1;
    font-weight: 400;
}

.files-changed-actions .action-btn .codicon {
    font-size: 11px;
    line-height: 1;
}

.no-changes {
    padding: 24px 12px;
    text-align: center;
    color: var(--vscode-descriptionForeground);
    font-size: 13px;
    font-style: italic;
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 8px;
    border-bottom: 1px solid var(--vscode-panel-border);
    cursor: pointer;
    min-height: 22px;
    font-size: 13px;
}

.file-item:hover {
    background-color: var(--vscode-list-hoverBackground);
}

.file-item:last-child {
    border-bottom: none;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 6px;
    flex: 1;
    min-width: 0;
}

.file-icon {
    font-size: 14px;
    color: var(--vscode-foreground);
    flex-shrink: 0;
}

.file-details {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 0;
}

.file-name {
    font-size: 13px;
    color: var(--vscode-foreground);
    font-family: var(--vscode-editor-font-family);
    font-weight: 400;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-path {
    font-size: 13px;
    color: var(--vscode-descriptionForeground);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
}

.file-changes {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-family: var(--vscode-editor-font-family, 'Consolas', monospace);
}

.changes-added {
    color: #89d185;
    font-weight: 400;
}

.changes-deleted {
    color: #f85149;
    font-weight: 400;
}

.file-actions {
    display: flex;
    gap: 2px;
    opacity: 0;
}

.file-item:hover .file-actions {
    opacity: 1;
}

.file-action-btn {
    background: none;
    border: none;
    color: var(--vscode-descriptionForeground);
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    font-size: 11px;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;

}

.file-action-btn:hover {
    background-color: var(--vscode-toolbar-hoverBackground);
    color: var(--vscode-foreground);
}

.file-action-btn.open-file:hover {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

.file-action-btn.delete-file:hover {
    background-color: #f87171;
    color: white;
}
