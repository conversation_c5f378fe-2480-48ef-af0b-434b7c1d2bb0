.header {
    background-color: var(--vscode-sideBar-background);
    border-bottom: 1px solid var(--vscode-panel-border);
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
}

.header.collapsed {
    max-height: 48px;
    min-height: 48px;
}

.header.expanded {
    max-height: 40vh;
    min-height: 200px;
}

.header-top {
    padding: 8px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--vscode-panel-border);
    background-color: var(--vscode-titleBar-activeBackground);
}

.threads-toggle-btn {
    background: rgba(0, 0, 0, 0.0);
    color: var(--vscode-button-foreground);
    border: none;
    padding: 6px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: background-color 0.2s;
}

.threads-toggle-btn:hover {
    background: var(--vscode-button-hoverBackground);
}

.threads-toggle-btn.active {
    background: var(--vscode-button-hoverBackground);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 6px;
}

.threads-container {
    flex: 1;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--vscode-scrollbarSlider-background) transparent;
    display: none;
    transition: all 0.3s ease;
}

.threads-container.show {
    display: block;
}

.threads-container::-webkit-scrollbar {
    width: 4px;
}

.threads-container::-webkit-scrollbar-track {
    background: transparent;
}

.threads-container::-webkit-scrollbar-thumb {
    background: var(--vscode-scrollbarSlider-background);
    border-radius: 2px;
}

.threads-container::-webkit-scrollbar-thumb:hover {
    background: var(--vscode-scrollbarSlider-hoverBackground);
}

.header h3 {
    margin: 0;
    color: var(--vscode-sideBarTitle-foreground);
    display: flex;
    align-items: center;
    gap: 8px;
}

.header-icon {
    font-size: 16px;
    margin-right: 4px;
}

/* Header buttons unified style */
.header-btn {
    background: rgba(0, 0, 0, 0.0);
    color: var(--vscode-button-foreground);
    border: 1px solid transparent;
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    width: 32px;
    height: 32px;
}

.header-btn:hover {
    background: var(--vscode-button-hoverBackground);
    border-color: var(--vscode-button-border);
}

.header-btn:active {
    transform: scale(0.95);
}

/* Specific button styles */
.clear-btn:hover {
    background: var(--vscode-errorBackground);
    border-color: var(--vscode-errorForeground);
    color: var(--vscode-errorForeground);
}

.new-chat-btn:hover {
    background: var(--vscode-button-background);
    border-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

.threads-section {
    border-bottom: 1px solid var(--vscode-panel-border);
}

.threads-section:last-child {
    border-bottom: none;
}

.threads-header {
    padding: 6px 12px;
    background-color: var(--vscode-editor-background);
    font-size: 10px;
    color: var(--vscode-descriptionForeground);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid var(--vscode-panel-border);
}

.thread-item {
    padding: 4px 12px;
    cursor: pointer;
    border-bottom: 1px solid var(--vscode-panel-border);
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    background: transparent;
    min-height: 32px;
}

.thread-item:hover {
    background-color: var(--vscode-list-hoverBackground);
}

.thread-item.active {
    background-color: var(--vscode-list-activeSelectionBackground);
    color: var(--vscode-list-activeSelectionForeground);
}

.thread-item:last-child {
    border-bottom: none;
}

.thread-info {
    flex: 1;
    min-width: 0;
}

.thread-title {
    font-size: 12px;
    color: var(--vscode-foreground);
    font-weight: 400;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 1px;
    line-height: 1.2;
}

.thread-preview {
    font-size: 10px;
    color: var(--vscode-descriptionForeground);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    opacity: 0.8;
    line-height: 1.1;
}

.thread-meta {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 9px;
    color: var(--vscode-descriptionForeground);
    opacity: 0.7;
    margin-top: 1px;
}

.thread-time {
    font-size: 9px;
}

.thread-message-count {
    background: var(--vscode-badge-background);
    color: var(--vscode-badge-foreground);
    padding: 0px 3px;
    border-radius: 7px;
    font-size: 8px;
    font-weight: 500;
    min-width: 14px;
    text-align: center;
    line-height: 1.3;
}

.thread-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.thread-item:hover .thread-actions {
    opacity: 1;
}

.thread-action-btn {
    background: none;
    border: none;
    color: var(--vscode-descriptionForeground);
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    font-size: 11px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    width: 16px;
    height: 16px;
}

.thread-action-btn:hover {
    background: var(--vscode-toolbar-hoverBackground);
    color: var(--vscode-foreground);
}

.thread-action-btn.delete:hover {
    background: var(--vscode-errorBackground);
    color: var(--vscode-errorForeground);
}

.no-threads {
    padding: 16px 12px;
    text-align: center;
    color: var(--vscode-descriptionForeground);
    font-size: 12px;
    font-style: italic;
}


/* Responsive design for smaller screens */
@media (max-width: 480px) {
    .header.expanded {
        max-height: 30vh;
        min-height: 150px;
    }

    .header.collapsed {
        max-height: 40px;
        min-height: 40px;
    }

    .header-top {
        padding: 6px 8px;
    }

    .threads-toggle-btn {
        font-size: 11px;
        padding: 4px 6px;
    }

    .thread-item {
        padding: 3px 8px;
        min-height: 28px;
    }

    .thread-title {
        font-size: 11px;
    }

    .thread-preview {
        font-size: 9px;
    }

    .thread-meta {
        font-size: 8px;
    }

    .files-changed-actions {
        gap: 4px;
    }

    .files-changed-actions .action-btn {
        padding: 3px 4px;
        font-size: 10px;
        height: 20px;
        min-width: 20px;
    }

    .action-text {
        display: none;
    }

    .files-changed-actions .action-btn .codicon {
        font-size: 10px;
    }
}
