.input-container {
    margin: 6px 12px 12px 12px;
    border: 1px solid var(--vscode-panel-border);
    border-radius: 8px;
    background-color: var(--vscode-sideBar-background);
    overflow: hidden;
}

.input-row {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    background: var(--vscode-sideBar-background);
    padding: 12px;
}

.input-container:focus-within {
    border-color: var(--vscode-focusBorder);
}

.input-wrapper {
    flex: 1;
    display: flex;
    align-items: flex-start;
    gap: 6px;
    padding-top: 2px;
}

.input-icon {
    color: var(--vscode-descriptionForeground);
    font-size: 14px;
    opacity: 0.6;
    flex-shrink: 0;
    margin-top: 2px;
}

.message-input {
    flex: 1;
    background: transparent;
    color: var(--vscode-input-foreground);
    border: none;
    padding: 0;
    font-family: var(--vscode-font-family);
    font-size: 13px;
    resize: none;
    min-height: 20px;
    max-height: 100px;
    line-height: 20px;
    vertical-align: top;
}

.message-input:focus {
    outline: none;
}

.message-input::placeholder {
    color: var(--vscode-input-placeholderForeground);
    opacity: 0.7;
}

.send-btn {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    flex-shrink: 0;
    margin-top: 0;
}

.send-btn:hover:not(:disabled) {
    background: var(--vscode-button-hoverBackground);
}

.send-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background: var(--vscode-input-background);
}

.send-btn .btn-icon {
    font-size: 14px;
    margin: 0;
}

.send-btn-text {
    display: none;
}


/* Message edit interface */
.message-edit-container {
    background-color: var(--vscode-input-background);
    border: 1px solid var(--vscode-focusBorder);
    border-radius: 8px;
    padding: 12px;
    margin: 4px 0;
}

.message-edit-input {
    width: 100%;
    background: transparent;
    color: var(--vscode-input-foreground);
    border: none;
    padding: 8px 0;
    font-family: var(--vscode-font-family);
    font-size: 13px;
    resize: vertical;
    min-height: 60px;
    max-height: 200px;
    line-height: 1.4;
}

.message-edit-input:focus {
    outline: none;
}

.message-edit-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    justify-content: flex-end;
}

.edit-action-btn {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: background-color 0.2s;
}

.edit-action-btn:hover {
    background: var(--vscode-button-hoverBackground);
}

.edit-action-btn.cancel {
    background: var(--vscode-input-background);
    color: var(--vscode-foreground);
    border: 1px solid var(--vscode-panel-border);
}

.edit-action-btn.cancel:hover {
    background: var(--vscode-list-hoverBackground);
}
