/* Override Prism.js colors to match VS Code dark theme */
.code-block-content .token.comment,
.code-block-content .token.prolog,
.code-block-content .token.doctype,
.code-block-content .token.cdata {
    color: var(--syntax-comment);
    font-style: italic;
}

.code-block-content .token.punctuation {
    color: var(--syntax-punctuation);
}

.code-block-content .token.property,
.code-block-content .token.tag,
.code-block-content .token.boolean,
.code-block-content .token.number,
.code-block-content .token.constant,
.code-block-content .token.symbol,
.code-block-content .token.deleted {
    color: #B5CEA8;
}

.code-block-content .token.selector,
.code-block-content .token.attr-name,
.code-block-content .token.string,
.code-block-content .token.char,
.code-block-content .token.builtin,
.code-block-content .token.inserted {
    color: #CE9178;
}

.code-block-content .token.operator,
.code-block-content .token.entity,
.code-block-content .token.url,
.code-block-content .language-css .token.string,
.code-block-content .style .token.string {
    color: #D4D4D4;
}

.code-block-content .token.atrule,
.code-block-content .token.attr-value,
.code-block-content .token.keyword {
    color: #569CD6;
}

.code-block-content .token.function,
.code-block-content .token.class-name {
    color: #DCDCAA;
}

.code-block-content .token.regex,
.code-block-content .token.important,
.code-block-content .token.variable {
    color: #9CDCFE;
}

/* Additional VS Code theme colors for better syntax highlighting */
.code-block-content .token.namespace {
    color: #4EC9B0;
}

.code-block-content .token.type-annotation,
.code-block-content .token.type {
    color: #4EC9B0;
}

.code-block-content .token.parameter {
    color: #9CDCFE;
}

.code-block-content .token.annotation,
.code-block-content .token.decorator {
    color: #DCDCAA;
}

.code-block-content .token.macro {
    color: #569CD6;
}

.code-block-content .token.escape {
    color: #D7BA7D;
}

.code-block-content .token.bold {
    font-weight: bold;
}

.code-block-content .token.italic {
    font-style: italic;
}

/* Additional token types for comprehensive language support */
.code-block-content .token.tag .token.punctuation,
.code-block-content .token.attr-name,
.code-block-content .token.attr-value .token.punctuation {
    color: #92C5F8;
}

.code-block-content .token.doctype,
.code-block-content .token.prolog {
    color: #6A9955;
    font-style: italic;
}

.code-block-content .token.script,
.code-block-content .token.style {
    color: #D4D4D4;
}

.code-block-content .token.interpolation,
.code-block-content .token.interpolation-punctuation {
    color: #FF8C94;
}

.code-block-content .token.template-string,
.code-block-content .token.template-punctuation {
    color: #CE9178;
}

.code-block-content .token.generic,
.code-block-content .token.generic.inserted {
    color: #B5CEA8;
}

.code-block-content .token.generic.deleted {
    color: #F44747;
}

.code-block-content .token.generic.heading {
    color: #569CD6;
    font-weight: bold;
}

.code-block-content .token.generic.subheading {
    color: #4EC9B0;
    font-weight: bold;
}

.code-block-content .token.generic.strong {
    font-weight: bold;
}

.code-block-content .token.generic.emph {
    font-style: italic;
}

/* Language-specific token colors */
.code-block-content .token.sql-keyword {
    color: #569CD6;
    text-transform: uppercase;
}

.code-block-content .token.json-property {
    color: #9CDCFE;
}

.code-block-content .token.yaml-anchor,
.code-block-content .token.yaml-alias {
    color: #DCDCAA;
}

.code-block-content .token.diff.inserted {
    background-color: rgba(155, 185, 85, 0.2);
    color: #9BB955;
}

.code-block-content .token.diff.deleted {
    background-color: rgba(244, 71, 71, 0.2);
    color: #F44747;
}

.message code {
    background-color: var(--vscode-textCodeBlock-background);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: var(--vscode-editor-font-family);
    font-size: 13px;
    border: 1px solid var(--vscode-input-border);
}

/* Inline code syntax highlighting */
.message code:not(pre code) .token.comment,
.message code:not(pre code) .token.prolog,
.message code:not(pre code) .token.doctype,
.message code:not(pre code) .token.cdata {
    color: #6A9955;
    font-style: italic;
}

.message code:not(pre code) .token.punctuation {
    color: #D4D4D4;
}

.message code:not(pre code) .token.property,
.message code:not(pre code) .token.tag,
.message code:not(pre code) .token.boolean,
.message code:not(pre code) .token.number,
.message code:not(pre code) .token.constant,
.message code:not(pre code) .token.symbol {
    color: #B5CEA8;
}

.message code:not(pre code) .token.selector,
.message code:not(pre code) .token.attr-name,
.message code:not(pre code) .token.string,
.message code:not(pre code) .token.char,
.message code:not(pre code) .token.builtin {
    color: #CE9178;
}

.message code:not(pre code) .token.operator,
.message code:not(pre code) .token.entity,
.message code:not(pre code) .token.url {
    color: #D4D4D4;
}

.message code:not(pre code) .token.atrule,
.message code:not(pre code) .token.attr-value,
.message code:not(pre code) .token.keyword {
    color: #569CD6;
}

.message code:not(pre code) .token.function,
.message code:not(pre code) .token.class-name {
    color: #DCDCAA;
}

.message code:not(pre code) .token.regex,
.message code:not(pre code) .token.important,
.message code:not(pre code) .token.variable {
    color: #9CDCFE;
}

/* Additional inline code highlighting */
.message code:not(pre code) .token.namespace {
    color: #4EC9B0;
}

.message code:not(pre code) .token.type-annotation,
.message code:not(pre code) .token.type {
    color: #4EC9B0;
}

.message code:not(pre code) .token.parameter {
    color: #9CDCFE;
}

.message code:not(pre code) .token.annotation,
.message code:not(pre code) .token.decorator {
    color: #DCDCAA;
}

.message code:not(pre code) .token.bold {
    font-weight: bold;
}

.message code:not(pre code) .token.italic {
    font-style: italic;
}

.message pre code {
    background: none;
    padding: 0;
    border: none;
    font-size: inherit;
}
