#!/usr/bin/env python3
"""
Simple test for Augura Agent
"""

import sys
import os
import asyncio

# Add agent_core to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'agent_core'))

from config import get_config
from ai_agent import AIAgent, AgentConfig as AIAgentConfig

async def test_agent():
    """Test the agent with a simple query"""
    try:
        print("🚀 Testing Augura Coder Agent...")
        
        # Load configuration
        config = get_config(".")
        print("✅ Configuration loaded")
        
        # Create AI agent configuration
        ai_config = AIAgentConfig(
            openrouter_api_key=config.api.openrouter_api_key,
            groq_api_key=config.api.groq_api_key,
            default_model=config.api.default_model,
            groq_model=config.api.groq_model,
            max_tokens=config.api.max_tokens,
            temperature=config.api.temperature,
            memory_dir=config.memory.memory_dir,
            index_db=config.indexing.index_db_path,
            enable_learning=config.memory.enabled,
            enable_memory=config.memory.enabled,
            enable_rag=config.rag.enabled
        )
        
        # Initialize AI agent
        agent = AIAgent(ai_config, ".")
        await agent.initialize()
        print("✅ AI Agent initialized")
        
        # Test query
        print("\n🤖 Testing query...")
        response = await agent.process_query("Hello! Can you help me with coding?")
        
        print(f"\n📝 Response:")
        print(response.content)
        print(f"\n📊 Confidence: {response.confidence:.2f}")
        print(f"⏱️  Execution time: {response.execution_time:.2f}s")
        
        # Cleanup
        await agent.cleanup()
        print("\n✅ Test completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_agent())
